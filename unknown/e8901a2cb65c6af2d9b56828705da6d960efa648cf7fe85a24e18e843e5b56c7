<?php $__env->startSection('title'); ?>
Users - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
    <!-- Start datatable css -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.19/css/jquery.dataTables.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.18/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap.min.css">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>

<!-- Page Title Area Start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Users</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><span>All Users</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- Page Title Area End -->

<div class="main-content-inner">
    <div class="row">
        <!-- Data Table Start -->
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title float-left">Users List</h4>
                    <p class="float-right mb-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('admin.users.create')): ?>
                            <a class="btn btn-primary text-white" href="<?php echo e(route('admin.users.create')); ?>">Create New User</a>
                        <?php endif; ?>
                    </p>
                    <div class="clearfix"></div>
                    <div class="data-tables">
                        <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <table id="dataTable" class="text-center">
                           <thead class="bg-light text-capitalize">
								<tr>
									<th><?php echo e(__('Name')); ?></th> <!-- Устанавливаем фиксированную ширину для Name -->
									<th><?php echo e(__('Email')); ?></th> <!-- Устанавливаем фиксированную ширину для Email -->
									<th><?php echo e(__('Roles')); ?></th> <!-- Устанавливаем фиксированную ширину для Roles -->
									<th><?php echo e(__('Action')); ?></th> <!-- Устанавливаем фиксированную ширину для Action -->
								</tr>
							</thead>
							<tbody>
							   <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
							   <tr>
									<td><?php echo e($user->username); ?></td>
									<td><?php echo e($user->email); ?></td>
									<td>
										<?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
											<span class="badge badge-info mr-1"><?php echo e($role->name); ?></span>
										<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
									</td>
									<td style="white-space: nowrap;"> <!-- Применяем nowrap для предотвращения переноса кнопок -->
										<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('admin.users.edit')): ?>
											<a class="btn btn-success btn-sm text-white" href="<?php echo e(route('admin.users.edit', $user->id)); ?>">Edit</a>
										<?php endif; ?>
										
										<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('admin.users.delete')): ?>
										<a class="btn btn-danger btn-sm text-white" href="<?php echo e(route('admin.users.destroy', $user->id)); ?>"
										   onclick="event.preventDefault(); document.getElementById('delete-form-<?php echo e($user->id); ?>').submit();">
											Delete
										</a>
										<form id="delete-form-<?php echo e($user->id); ?>" action="<?php echo e(route('admin.users.destroy', $user->id)); ?>" method="POST" style="display: none;">
											<?php echo csrf_field(); ?>
											<?php echo method_field('DELETE'); ?>
										</form>
										<?php endif; ?>
									</td>
								</tr>
							   <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
							</tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- Data Table End -->
        
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
     <!-- Start datatable js -->
     <script src="https://cdn.datatables.net/1.10.19/js/jquery.dataTables.js"></script>
     <script src="https://cdn.datatables.net/1.10.18/js/jquery.dataTables.min.js"></script>
     <script src="https://cdn.datatables.net/1.10.18/js/dataTables.bootstrap4.min.js"></script>
     <script src="https://cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>
     <script src="https://cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap.min.js"></script>
     
     <script>
         /* Datatable Initialization */
			$(document).ready(function() {
				$('#dataTable').DataTable({
					responsive: true,
					autoWidth: false, // Отключаем автоматическую ширину
					columnDefs: [
						{ "width": "10px", "targets": 0 }, // Ширина столбца Name
						{ "width": "25px", "targets": 1 }, // Ширина столбца Email
						{ "width": "50px", "targets": 2 }, // Ширина столбца Roles
						{ "width": "50px", "targets": 3 }, // Ширина столбца Action
					],
				});
			});
     </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/users/index.blade.php ENDPATH**/ ?>