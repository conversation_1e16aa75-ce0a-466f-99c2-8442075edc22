<!DOCTYPE html>
<!-- This site was created in Zer0lab. https://dev1.it.com -->
<!-- Last Published: Wed Feb 15 2025 00:00:00 GMT+0000 (Coordinated Universal Time) -->
<html data-wf-domain="techbetatemplates.webflow.io"
      data-wf-page="66465df4b6e51dd6844d9b70"
      data-wf-site="663b748d327c826a2952af46"
      data-wf-status="1"
      lang="en"
      class=" w-mod-js w-mod-ix">
  <head>
    <meta charset="utf-8" />
    <title>EVO shop - Digital Certificates Store</title>
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <!-- При необходимости добавьте свои стили, шрифты и другие метатеги -->

    <style>
      /* Стили пагинации */
      .pagination {
        text-align: center; 
        margin-top: 30px;
      }
      .page-btn {
        padding: var(--components--buttons--paddings--pd-regular) var(--components--buttons--paddings--pd-medium);
        grid-column-gap: var(--components--buttons--gaps--gap-small);
        grid-row-gap: var(--components--buttons--gaps--gap-small);
        border: var(--components--buttons--border-width--bw-default) solid var(--components--buttons-primary--border-color--b-light-mode);
        border-radius: var(--components--buttons--border-radius--br-regular);
        background-color: var(--components--buttons-primary--backgrounds--bg-light-mode);
        box-shadow: 0 2px 4px 0 var(--core--box-shadow--bs-primary-regular);
        color: var(--components--buttons-primary--text--text-light-mode);
        font-size: var(--core--font-size--displays--display-2);
        line-height: var(--core--line-height--regular);
        text-align: center;
        transform-style: preserve-3d;
        justify-content: center;
        align-items: center;
        font-weight: 500;
        text-decoration: none;
        transition: transform .3s;
        cursor: pointer;
      }
      .page-btn:hover {
        color: var(--components--buttons-primary--text--text-light-mode);
        transform: scale3d(.94, .94, 1.01);
      }
      .page-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      /* Если товара нет, делаем карточку слегка серой, неактивной */
      .out-of-stock-card {
        background-color: #f7f7f7; 
        opacity: 0.7;             
        pointer-events: none;     
      }

      /* Бейдж для статуса наличия товара (in stock / out of stock) */
      .stock-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 2;
        font-size: 14px;
        font-weight: 500;
        padding: 4px 8px;
        border-radius: 4px;
        background-color: #ffffff;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
      }
      .stock-badge.out-of-stock {
        color: #c00;
      }

      /* Пример стиля для «click to view» */
      .link.mid.no-hover {
        font-weight: 500;
        font-size: 16px;
      }

      /*
         =========================
         Стили для модального окна 
         =========================
      */
      .modal {
        display: none; /* по умолчанию скрыто */
        position: fixed;
        z-index: 999999; 
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.5); 
      }
      .modal-content {
        background-color: #fff;
        margin: 5% auto; 
        padding: 20px;
        border-radius: 8px;
        max-width: 600px;
        position: relative;
      }
      .modal-header {
        margin-bottom: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .modal-header h2 {
        margin: 0;
      }
      .modal-close-btn {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
      }
      /* 
         Список покупок 
      */
      .purchases-list {
        max-height: 400px; 
        overflow-y: auto;
        margin-bottom: 20px;
      }
      .purchase-item-card {
        border: 1px solid #e6e6e6;
        border-radius: 6px;
        padding: 15px 20px;
        margin-bottom: 10px;
        background-color: #fafafa;
      }
      .purchase-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }
      .purchase-title {
        font-size: 16px;
        font-weight: 600;
      }
      .purchase-price {
        font-size: 16px;
        font-weight: 600;
        color: #212529;
        background: #f1f1f1;
        border-radius: 4px;
        padding: 4px 8px;
      }
      .purchase-date {
        font-size: 14px;
        color: #666;
      }
      /* Кнопки пагинации внутри модалки */
      #purchases-pagination .page-btn {
        margin: 0 5px;
      }

      /* Адаптив */
      @media screen and (max-width: 767px) {
        .modal-content {
          margin: 10% auto;
          width: 90%;
        }
      }
    </style>
  </head>
  <body>
    <div class="page-wrapper">
      <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
      <div class="w-nav-overlay" data-wf-ignore="" id="w-nav-overlay-0"></div>
    </div>

    <section class="section top small">
      <div class="w-layout-blockcontainer container-default w-container">
        <div class="w-layout-grid grid-2-columns hero-grid---integrations-page">
          <div id="w-node-_72511e53-6547-f24d-47bd-f00cd8fba300-844d9b70"
               class="inner-container _375px _100-mbp">
            <div class="text-center-mbl">
              <div
                data-w-id="10ca3b57-1247-179b-dcfc-250451af759b"
                style="transform: translate3d(0px, 0%, 0px) scale3d(1,1,1)
                       rotateX(0deg) rotateY(0deg) rotateZ(0deg)
                       skew(0deg,0deg); opacity: 1; transform-style: preserve-3d;"
                class="flex-vertical align-start center---mbl"
              >
                <div class="text-break-all---mbp">
                  <h1 class="display-9 mid title-tag">EVO shop</h1>
                </div>
                <div class="mg-top-small mg-top-16px---mbl">
                  <p>
                    Digital Store for Certificates, Vouchers & PIN Codes
Enjoy unique cashback rewards and member-only benefits on every purchase.
                  </p>
                </div>
                <div class="mg-top-default mg-top-24px---mbl width-100-mobile-portrait">
                  <div class="buttons-row left">
                    <a id="w-node-_9ac5c108-4a68-32dd-6e03-9f793bb88842-3bb88842"
                       href="#integrations"
                       class="secondary-button w-inline-block"
                       style="margin-right: 10px;">
                      <div class="text-block">See All Certificates</div>
                    </a>
                    <!-- Кнопка для открытия модального окна "Мои покупки" -->
                    <a id="my-purchases-btn" 
                       href="javascript:void(0);"
                       class="secondary-button w-inline-block">
                      <div class="text-block">My Certificates</div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
			<div id="w-node-_3d7ff589-0a3f-572d-4438-9ba31bb2f8f9-844d9b70" data-w-id="3d7ff589-0a3f-572d-4438-9ba31bb2f8f9" style="transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg); opacity: 1; transform-style: preserve-3d;" class="card hero-card---integrations">
			   <div data-w-id="08d09579-ac06-1034-1aae-fd854c5899a8" class="marquee-wrapper">
				  <div class="logo-marquee marquee" style="transform: translate3d(-17.708%, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg); transform-style: preserve-3d; will-change: transform;">
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div></div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div>🌎</div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div>💲</div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div>💻</div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div></div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div>🛜</div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div>🇦🇮</div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div>֎</div>
						</div>
					 </div>
				  </div>
				  <div class="logo-marquee marquee" style="transform: translate3d(-17.708%, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg); transform-style: preserve-3d; will-change: transform;">
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div>𝄃𝄃𝄂𝄂𝄀𝄁𝄃𝄂𝄂𝄃</div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div>💎</div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div>🏦</div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div></div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div>💵</div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div>📲</div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div></div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div></div>
						</div>
					 </div>
				  </div>
				  <div class="logo-marquee marquee" style="transform: translate3d(-17.708%, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg); transform-style: preserve-3d; will-change: transform;">
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div></div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div></div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div></div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div></div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div></div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div></div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div></div>
						</div>
					 </div>
					 <div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						<div class="icon-font-social-media">
						   <div></div>
						</div>
					 </div>
				  </div>
			   </div>
			   <div class="mg-top-small">
				  <div data-w-id="1fdb89b3-1f94-8e9d-2f77-6fecadced157" class="marquee-wrapper">
					 <div class="logo-marquee marquee" style="transform: translate3d(-82.292%, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg); transform-style: preserve-3d; will-change: transform;">
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div>🔐</div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div></div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div>💹</div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div></div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div></div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div></div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div>💳</div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div></div>
						   </div>
						</div>
					 </div>
					 <div class="logo-marquee marquee" style="transform: translate3d(-82.292%, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg); transform-style: preserve-3d; will-change: transform;">
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div></div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div>🔝</div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div>🌱</div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div></div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div>🔗</div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div></div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div>🤝</div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div></div>
						   </div>
						</div>
					 </div>
					 <div class="logo-marquee marquee" style="transform: translate3d(-82.292%, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg); transform-style: preserve-3d; will-change: transform;">
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div></></div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div>📈</div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div></div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div>🎯</div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div></div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div></div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div></div>
						   </div>
						</div>
						<div id="w-node-_9fc60372-5309-4e16-d1f7-da03b6995c9b-b6995c9b" class="card logo-strip-v12">
						   <div class="icon-font-social-media">
							  <div>📲</div>
						   </div>
						</div>
					 </div>
				  </div>
			   </div>
			</div>
        </div>
      </div>
    </section>

    <section id="integrations" class="section small bottom-v2">
      <div class="w-layout-blockcontainer container-default w-container">
        <div class="text-center-mbl">
          <div
            data-w-id="0cdb6d37-381d-7c43-57fb-24882cc1868b"
            style="transform: translate3d(0px, 0%, 0px) scale3d(1,1,1)
                   rotateX(0deg) rotateY(0deg) rotateZ(0deg)
                   skew(0deg,0deg); opacity: 1; transform-style: preserve-3d;"
            class="title-left---content-right center---mbl"
          >
            <div class="width-100-mbl">
              <h2 class="display-8 mid">Digital Goods</h2>
            </div>
            <div class="position-relative mg-bottom-0 w-form">
              <label for="search" class="hidden">Search</label>
              <input class="input button-inside icon-inside w-input"
                     maxlength="256"
                     name="query"
                     placeholder="Search for products…"
                     type="search"
                     id="search"
                     required="">
              <input type="submit"
                     class="primary-button inside-input button-icon w-button"
                     value="">
            </div>
          </div>
        </div>

        <!-- Контейнер для списка товаров -->
        <div class="mg-top-extra-large mg-top-48px---mbl">
          <div class="w-dyn-list">
            <div role="list"
                 class="grid-3-columns integrations-grid w-dyn-items"
                 id="product-list">
              <!-- Товары добавятся динамически через JS -->
            </div>
          </div>
        </div>

        <!-- Пагинация (продуктовая) -->
        <div class="pagination">
          <button id="prev-page-btn" class="page-btn" style="display: none;">← Back</button>
          <button id="next-page-btn" class="page-btn" style="display: none;">Next →</button>
        </div>
      </div>
    </section>

    <footer data-w-id="4ce7d77c-4018-d530-59d0-9a7e57241a43"
            class="footer-wrapper"
            style="opacity: 1;">
      <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </footer>

    <!-- ===========================
         Модальное окно "Мои покупки"
         =========================== -->
    <div id="purchases-modal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>My Purchases</h2>
          <button class="modal-close-btn" id="close-purchases-modal">✕</button>
        </div>
        <div class="purchases-list" id="purchases-list">
          <!-- Список покупок будет загружаться динамически -->
        </div>
        <div class="pagination" id="purchases-pagination" style="display: none;">
          <button id="prev-purchase-page" class="page-btn" style="display: none;">← Back</button>
          <button id="next-purchase-page" class="page-btn" style="display: none;">Next →</button>
        </div>
      </div>
    </div>

    <!-- Скрипты -->
    <script
      src="js/jquery-3.5.1.min.dc5e7f18c8.js"
      type="text/javascript"
      integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="
      crossorigin="anonymous">
    </script>
    <script src="js/webflow.3de03aa26.js" type="text/javascript"></script>
    <script type="text/javascript">
      // Функция для форматирования даты в человекочитаемый вид
      function formatDateTime(datetimeStr) {
        // Создаем объект Date
        const dateObj = new Date(datetimeStr);
        // Проверка, что дата корректна
        if (isNaN(dateObj.getTime())) return datetimeStr;
        // Возвращаем локальную строку (можно менять опции)
        return dateObj.toLocaleString([], { 
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }

      $(document).ready(function () {
        /*
          =======================
          Подгрузка товаров
          =======================
        */
        let currentPage = 1;
        let totalPages = 1;

        function fetchProducts(page, search = '') {
          $.ajax({
            url: "/api/products_search?page=" + page + "&search=" + search,
            method: "GET",
            success: function (response) {
              // Очищаем список
              $("#product-list").empty();

              // Извлекаем данные
              const products = response.data || [];
              totalPages = response.meta.total_pages || 1;
              currentPage = response.meta.current_page || 1;

              // Рендер товаров
              products.forEach(function (item) {
                const productId = item._id;
                const title = item.title;
                const shortDesc = item.short_description;
                const price = item.price;
                const category = item.category;
                const storageCount = item.storage_count;

                let outOfStock = storageCount <= 0;
                let outOfStockClass = outOfStock ? "out-of-stock-card" : "";

                let stockBadgeText = outOfStock ? "Out of stock" : "In stock";
                let stockBadgeClass = outOfStock ? "out-of-stock" : "in-stock";

                let bottomBlock = outOfStock
                  ? ""
                  : `
                    <div class="link mid no-hover">
                      <div>click to view</div>
                      <div class="arrow-icon-wrapper">
                        <div class="icon-font-rounded"></div>
                      </div>
                    </div>
                  `;

                const productCard = `
                  <div id="dynamic-item-${productId}"
                       role="listitem"
                       class="w-dyn-item">
                    <a
                      style="opacity: 1; position: relative;"
                      href="${ outOfStock ? "javascript:void(0);" : "/product_view?id=" + productId }"
                      class="card integration-card w-inline-block ${outOfStockClass}"
                    >
                      <div class="stock-badge ${stockBadgeClass}">
                        ${stockBadgeText}
                      </div>
                      <div class="mg-bottom-small mg-bottom-16px---mbl"></div>
                      <div class="inner-container _500px---mbl">
                        <h3 class="display-5 mid title" style="opacity: 1;">${title}</h3>
                        <p class="mg-top-extra-small">${shortDesc}</p>
                      </div>
                      <div class="mg-top-default mg-top-24px---mbl">
                        ${bottomBlock}
                      </div>
                      <div
                        style="
                          position: absolute;
                          bottom: 10px;
                          right: 10px;
                          background-color: #f8f9fa;
                          color: #212529;
                          padding: 5px 10px;
                          border-radius: 4px;
                          font-size: 20px;
                        "
                      >
                        ${price} USDZ
                      </div>
                      <div class="integration-badge-wrapper">
                        <div class="badge mid small">
                          <div>${category}</div>
                        </div>
                      </div>
                    </a>
                  </div>
                `;
                $("#product-list").append(productCard);
              });

              updatePagination();
            },
            error: function (err) {
              console.error("Unable to Retrieve Products:", err);
            },
          });
        }

        function updatePagination() {
          if (currentPage > 1) {
            $("#prev-page-btn").show();
          } else {
            $("#prev-page-btn").hide();
          }
          if (currentPage < totalPages) {
            $("#next-page-btn").show();
          } else {
            $("#next-page-btn").hide();
          }
        }

        $("#prev-page-btn").click(function () {
          if (currentPage > 1) {
            fetchProducts(--currentPage, $('#search').val().trim());
          }
        });
        $("#next-page-btn").click(function () {
          if (currentPage < totalPages) {
            fetchProducts(++currentPage, $('#search').val().trim());
          }
        });

        // Загрузка товаров при старте
        fetchProducts(currentPage);

        // Фильтр по поиску
        $('#search').on('input', function() {
          fetchProducts(1, $(this).val().trim());
        });


        /*
          =======================
          Мои покупки (модалка)
          =======================
        */
        let purchasesPage = 1;
        let purchasesLastPage = 1;

        function fetchPurchases(page) {
          $.ajax({
            url: "/api/get_purchases?page=" + page,
            method: "GET",
            success: function (resp) {
              const data = resp.data || [];
              purchasesLastPage = resp.last_page || 1;
              purchasesPage = resp.current_page || 1;
              
              // Очищаем список покупок
              $("#purchases-list").empty();

              // Генерируем HTML по каждой покупке
              data.forEach((purchase) => {
                // Приводим дату в человекочитаемый формат
                const niceDate = formatDateTime(purchase.created_at);
                // Генерируем карточку
                let itemHtml = `
                  <div class="purchase-item-card">
                    <div class="purchase-header">
                      <div class="purchase-title">${purchase.description}</div>
                      <div class="purchase-price">${purchase.price} USDZ</div>
                    </div>
                    <div class="purchase-date">Purchased on: ${niceDate}</div>
                  </div>
                `;
                $("#purchases-list").append(itemHtml);
              });

              updatePurchasesPagination();
            },
            error: function (err) {
              console.error("Error Retrieving Purchases:", err);
            },
          });
        }

        function updatePurchasesPagination() {
          if (purchasesLastPage > 1) {
            $("#purchases-pagination").show();
          } else {
            $("#purchases-pagination").hide();
          }
          if (purchasesPage > 1) {
            $("#prev-purchase-page").show();
          } else {
            $("#prev-purchase-page").hide();
          }
          if (purchasesPage < purchasesLastPage) {
            $("#next-purchase-page").show();
          } else {
            $("#next-purchase-page").hide();
          }
        }

        $("#prev-purchase-page").click(function () {
          if (purchasesPage > 1) {
            fetchPurchases(--purchasesPage);
          }
        });
        $("#next-purchase-page").click(function () {
          if (purchasesPage < purchasesLastPage) {
            fetchPurchases(++purchasesPage);
          }
        });

        // Открыть модалку
        $("#my-purchases-btn").click(function () {
          purchasesPage = 1;
          fetchPurchases(purchasesPage);
          $("#purchases-modal").fadeIn();
        });

        // Закрыть модалку
        $("#close-purchases-modal").click(function () {
          $("#purchases-modal").fadeOut();
        });
      });
    </script>
  </body>
</html>
<?php /**PATH /var/www/club/resources/views/dashboard/shop.blade.php ENDPATH**/ ?>