<?php $__env->startSection('title', 'Payment Methods Page - Admin Panel'); ?>

<?php $__env->startSection('styles'); ?>
    <!-- Datatable CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.19/css/jquery.dataTables.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.18/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap.min.css">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>
<!-- Page Title Area -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Payment Methods</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><span>All Payment Methods</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- Page Title Area End -->

<div class="main-content-inner">
    <div class="row">
        <!-- Data Table Start -->
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title float-left">Payment Methods List</h4>
                    <?php if(auth()->user()->can('admin.payment_methods.create')): ?>
                        <a class="btn btn-primary float-right mb-2" href="<?php echo e(route('admin.payment_methods.create')); ?>">Create Payment Method</a>
                    <?php endif; ?>
                    <div class="clearfix"></div>
                    
                    <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <div class="data-tables">
                        <table id="dataTable" class="text-center">
                            <thead class="bg-light text-capitalize">
                                <tr>
                                    <th width="20%">Image</th>
                                    <th width="20%">Name</th>
                                    <th width="20%">Example Address</th>
                                    <th width="40%">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $payment_methods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment_method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <img src="<?php echo e($payment_method->img); ?>" alt="<?php echo e($payment_method->name); ?>" style="width: 50px; height: auto;">
                                    </td>
                                    <td><?php echo e($payment_method->name); ?></td>
                                    <td><?php echo e($payment_method->example_address); ?></td>
                                    <td>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('admin.payment_methods.edit')): ?>
                                            <a class="btn-sm btn-success text-white" href="<?php echo e(route('admin.payment_methods.edit', $payment_method->id)); ?>">Edit</a>
                                        <?php endif; ?>

                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('admin.payment_methods.delete')): ?>
                                            <a class="btn-sm btn-danger text-white" href="<?php echo e(route('admin.payment_methods.destroy', $payment_method->id)); ?>" 
                                               onclick="event.preventDefault(); document.getElementById('delete-form-<?php echo e($payment_method->id); ?>').submit();">
                                               Delete
                                            </a>
                                            <form id="delete-form-<?php echo e($payment_method->id); ?>" action="<?php echo e(route('admin.payment_methods.destroy', $payment_method->id)); ?>" method="POST" style="display: none;">
                                                <?php echo method_field('DELETE'); ?>
                                                <?php echo csrf_field(); ?>
                                            </form>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- Data Table End -->
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <!-- Datatable JS -->
    <script src="https://cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.18/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap.min.js"></script>
     
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                responsive: true
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/payment_methods/index.blade.php ENDPATH**/ ?>