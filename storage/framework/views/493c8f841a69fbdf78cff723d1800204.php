<?php $__env->startSection('title', 'Subscription Plans Management'); ?>

<?php $__env->startSection('styles'); ?>
<!-- DataTables -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.4.1/css/responsive.bootstrap4.min.css">
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>
<style>
    .plan-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        color: white;
        display: inline-block;
    }

    .status-active {
        background: #28a745;
    }

    .status-inactive {
        background: #dc3545;
    }

    .plan-features ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .plan-features li {
        font-size: 0.8rem;
        padding: 1px 0;
        color: #6c757d;
    }

    .plan-features li::before {
        content: '•';
        color: #28a745;
        margin-right: 5px;
    }

    .action-buttons {
        display: flex;
        gap: 3px;
        flex-wrap: wrap;
    }
</style>

<!-- Page Title -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Subscription Plans</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><span>Subscription Plans</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <div class="user-profile pull-right">
                <img class="avatar user-thumb" src="<?php echo e(asset('backend/assets/images/author/avatar.png')); ?>" alt="avatar">
                <h4 class="user-name dropdown-toggle" data-toggle="dropdown"><?php echo e(auth()->user()->name); ?> <i class="fa fa-angle-down"></i></h4>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="#">Message</a>
                    <a class="dropdown-item" href="#">Settings</a>
                    <a class="dropdown-item" href="#">Log Out</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<div class="main-content-inner">
    <div class="row">
        <!-- Data Table Start -->
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title float-left">Subscription Plans List</h4>
                    <div class="float-right mb-2">
                        <a class="btn btn-primary btn-sm" href="<?php echo e(route('admin.subscription-plans.create')); ?>">
                            <i class="fa fa-plus"></i> Create New Plan
                        </a>
                    </div>
                    <div class="clearfix"></div>

                    <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <div class="data-tables">
                        <table id="dataTable" class="text-center">
                            <thead class="bg-light text-capitalize">
                                <tr>
                                    <th width="20%">Plan</th>
                                    <th width="10%">Price</th>
                                    <th width="8%">Status</th>
                                    <th width="25%">Features</th>
                                    <th width="10%">Limits</th>
                                    <th width="8%">Active Users</th>
                                    <th width="19%">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="plan-badge mr-2" style="background-color: <?php echo e($plan->color ?? '#007bff'); ?>; width: 20px; height: 20px; border-radius: 50%;"></div>
                                            <div class="text-left">
                                                <strong><?php echo e($plan->display_name ?? $plan->name); ?></strong><br>
                                                <small class="text-muted"><?php echo e($plan->name); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>$<?php echo e(number_format($plan->monthly_price ?? 0, 2)); ?></strong>/mo
                                        <?php if(($plan->trial_days ?? 0) > 0): ?>
                                        <br><small class="text-info"><?php echo e($plan->trial_days); ?>d trial</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="plan-badge <?php echo e(($plan->is_active ?? true) ? 'status-active' : 'status-inactive'); ?>">
                                            <?php echo e(($plan->is_active ?? true) ? 'Active' : 'Inactive'); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <div class="plan-features text-left">
                                            <?php if(isset($plan->features) && is_array($plan->features) && count($plan->features) > 0): ?>
                                                <ul>
                                                    <?php $__currentLoopData = array_slice($plan->features, 0, 3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li><?php echo e($feature); ?></li>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php if(count($plan->features) > 3): ?>
                                                    <li><em>+<?php echo e(count($plan->features) - 3); ?> more...</em></li>
                                                    <?php endif; ?>
                                                </ul>
                                            <?php else: ?>
                                                <small class="text-muted">No features listed</small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <small>
                                            <strong>Cards:</strong> <?php echo e(($plan->max_business_cards ?? 1) == -1 ? '∞' : ($plan->max_business_cards ?? 1)); ?><br>
                                            <strong>Profiles:</strong> <?php echo e(($plan->max_club_profiles ?? 1) == -1 ? '∞' : ($plan->max_club_profiles ?? 1)); ?>

                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge badge-info"><?php echo e($plan->active_subscriptions ?? 0); ?></span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="<?php echo e(route('admin.subscription-plans.edit', $plan->_id)); ?>" class="btn btn-sm btn-success">
                                                <i class="fa fa-edit"></i> Edit
                                            </a>
                                            <button onclick="togglePlanStatus('<?php echo e($plan->_id); ?>', <?php echo e(($plan->is_active ?? true) ? 'false' : 'true'); ?>)"
                                                    class="btn btn-sm btn-warning ml-1">
                                                <i class="fa fa-toggle-<?php echo e(($plan->is_active ?? true) ? 'on' : 'off'); ?>"></i>
                                                <?php echo e(($plan->is_active ?? true) ? 'Disable' : 'Enable'); ?>

                                            </button>
                                            <?php if(($plan->active_subscriptions ?? 0) == 0): ?>
                                            <button onclick="deletePlan('<?php echo e($plan->_id); ?>', '<?php echo e($plan->display_name ?? $plan->name); ?>')"
                                                    class="btn btn-sm btn-danger ml-1">
                                                <i class="fa fa-trash"></i> Delete
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center">
                                        <p class="text-muted">No subscription plans found.</p>
                                        <a href="<?php echo e(route('admin.subscription-plans.create')); ?>" class="btn btn-primary">
                                            <i class="fa fa-plus"></i> Create Your First Plan
                                        </a>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- Data Table End -->
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.4.1/js/dataTables.responsive.min.js"></script>

<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            responsive: true,
            pageLength: 25,
            order: [[0, 'asc']],
            columnDefs: [
                { orderable: false, targets: [6] }
            ]
        });
    });

    function togglePlanStatus(planId, newStatus) {
        const action = newStatus === 'true' ? 'enable' : 'disable';

        Swal.fire({
            title: `${action.charAt(0).toUpperCase() + action.slice(1)} Plan?`,
            text: `Are you sure you want to ${action} this subscription plan?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#007bff',
            cancelButtonColor: '#6c757d',
            confirmButtonText: `Yes, ${action} it!`
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/admin/subscription-plans/${planId}/toggle-status`,
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        Swal.fire({
                            title: 'Success!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonColor: '#007bff'
                        }).then(() => {
                            window.location.reload();
                        });
                    },
                    error: function(xhr) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Failed to update plan status',
                            icon: 'error',
                            confirmButtonColor: '#dc3545'
                        });
                    }
                });
            }
        });
    }

    function deletePlan(planId, planName) {
        Swal.fire({
            title: 'Delete Plan?',
            text: `Are you sure you want to delete "${planName}"? This action cannot be undone.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/admin/subscription-plans/${planId}`,
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        Swal.fire({
                            title: 'Deleted!',
                            text: 'Subscription plan has been deleted.',
                            icon: 'success',
                            confirmButtonColor: '#007bff'
                        }).then(() => {
                            window.location.reload();
                        });
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON;
                        Swal.fire({
                            title: 'Error!',
                            text: response.message || 'Failed to delete plan',
                            icon: 'error',
                            confirmButtonColor: '#dc3545'
                        });
                    }
                });
            }
        });
    }


</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/subscription-plans/index.blade.php ENDPATH**/ ?>