<?php $__env->startSection('title'); ?>
    Club Profile Details - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
    <style>
        .profile-banner {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
        }
        .tag-item {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            margin: 2px;
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-active { background: #28a745; color: white; }
        .status-inactive { background: #6c757d; color: white; }
        .status-pending { background: #ffc107; color: #212529; }
        .status-approved { background: #28a745; color: white; }
        .status-rejected { background: #dc3545; color: white; }
        .info-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .info-value {
            color: #212529;
            margin-bottom: 15px;
        }
        .action-buttons {
            margin-top: 20px;
        }
        .action-buttons .btn {
            margin-right: 10px;
            margin-bottom: 10px;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>

<!-- Page Title Area Start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Club Profile Details</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><a href="<?php echo e(route('admin.club-profiles.index')); ?>">Club Profiles</a></li>
                    <li><span><?php echo e($profile->profile_name); ?></span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- Page Title Area End -->

<div class="main-content-inner">
    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Profile Information</h4>
                    
                    <?php if($profile->banner_image): ?>
                        <div class="mb-4">
                            <img src="<?php echo e($profile->banner_image); ?>" alt="Profile Banner" class="profile-banner">
                        </div>
                    <?php endif; ?>
                    
                    <div class="info-card">
                        <div class="info-label">Profile Name</div>
                        <div class="info-value"><?php echo e($profile->profile_name); ?></div>
                        
                        <div class="info-label">Display Name</div>
                        <div class="info-value"><?php echo e($profile->display_name ?? 'N/A'); ?></div>
                        
                        <div class="info-label">Description</div>
                        <div class="info-value"><?php echo e($profile->description ?? 'No description provided'); ?></div>
                        
                        <div class="info-label">Tags</div>
                        <div class="info-value">
                            <?php if($profile->tags && count($profile->tags) > 0): ?>
                                <?php $__currentLoopData = $profile->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="tag-item"><?php echo e($tag); ?></span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                No tags
                            <?php endif; ?>
                        </div>
                        
                        <div class="info-label">Public URL</div>
                        <div class="info-value">
                            <a href="<?php echo e(route('club.profile.public', $profile->profile_name)); ?>" target="_blank">
                                <?php echo e(route('club.profile.public', $profile->profile_name)); ?>

                                <i class="fa fa-external-link"></i>
                            </a>
                        </div>
                        
                        <div class="info-label">Views Count</div>
                        <div class="info-value"><?php echo e(number_format($profile->views_count)); ?> views</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Profile Status & Actions -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Status & Actions</h4>
                    
                    <div class="info-card">
                        <div class="info-label">Status</div>
                        <div class="info-value">
                            <span class="status-badge status-<?php echo e($profile->is_active ? 'active' : 'inactive'); ?>">
                                <?php echo e($profile->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </div>
                        
                        <div class="info-label">Moderation Status</div>
                        <div class="info-value">
                            <span class="status-badge status-<?php echo e($profile->moderation_status); ?>">
                                <?php echo e(ucfirst($profile->moderation_status)); ?>

                            </span>
                        </div>
                        
                        <?php if($profile->moderation_notes): ?>
                            <div class="info-label">Moderation Notes</div>
                            <div class="info-value"><?php echo e($profile->moderation_notes); ?></div>
                        <?php endif; ?>
                        
                        <div class="info-label">Created</div>
                        <div class="info-value"><?php echo e($profile->created_at->format('M d, Y H:i')); ?></div>
                        
                        <div class="info-label">Last Updated</div>
                        <div class="info-value"><?php echo e($profile->updated_at->format('M d, Y H:i')); ?></div>
                        
                        <?php if($profile->moderated_at): ?>
                            <div class="info-label">Moderated</div>
                            <div class="info-value"><?php echo e($profile->moderated_at->format('M d, Y H:i')); ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="action-buttons">
                        <a href="<?php echo e(route('admin.club-profiles.edit', $profile->_id)); ?>" class="btn btn-primary btn-sm">
                            <i class="fa fa-edit"></i> Edit Profile
                        </a>
                        
                        <?php if($profile->moderation_status == 'pending'): ?>
                            <button class="btn btn-success btn-sm approve-btn" data-id="<?php echo e($profile->_id); ?>">
                                <i class="fa fa-check"></i> Approve
                            </button>
                            <button class="btn btn-danger btn-sm reject-btn" data-id="<?php echo e($profile->_id); ?>">
                                <i class="fa fa-times"></i> Reject
                            </button>
                        <?php endif; ?>
                        
                        <a href="<?php echo e(route('club.profile.public', $profile->profile_name)); ?>" target="_blank" class="btn btn-info btn-sm">
                            <i class="fa fa-eye"></i> View Public
                        </a>
                        
                        <a href="<?php echo e(route('admin.club-profiles.index')); ?>" class="btn btn-secondary btn-sm">
                            <i class="fa fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- User Information -->
            <div class="card mt-4">
                <div class="card-body">
                    <h4 class="header-title">User Information</h4>
                    
                    <div class="info-card">
                        <?php if($profile->user->avatar): ?>
                            <div class="mb-3">
                                <img src="<?php echo e($profile->user->avatar); ?>" alt="User Avatar" class="profile-avatar">
                            </div>
                        <?php endif; ?>
                        
                        <div class="info-label">Name</div>
                        <div class="info-value"><?php echo e($profile->user->full_name ?? $profile->user->name ?? 'N/A'); ?></div>
                        
                        <div class="info-label">Email</div>
                        <div class="info-value"><?php echo e($profile->user->email); ?></div>
                        
                        <div class="info-label">User ID</div>
                        <div class="info-value"><?php echo e($profile->user->_id); ?></div>
                        
                        <div class="info-label">Joined</div>
                        <div class="info-value"><?php echo e($profile->user->created_at->format('M d, Y')); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Moderation Modal -->
<div class="modal fade" id="moderationModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Moderate Profile</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="moderationForm">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <input type="hidden" id="profileId" name="profile_id">
                    <input type="hidden" id="action" name="action">
                    
                    <div class="form-group">
                        <label for="notes">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Add moderation notes..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Approve button
    $('.approve-btn').click(function() {
        const profileId = $(this).data('id');
        $('#profileId').val(profileId);
        $('#action').val('approve');
        $('.modal-title').text('Approve Club Profile');
        $('#notes').attr('placeholder', 'Add approval notes (optional)...');
        $('#moderationModal').modal('show');
    });

    // Reject button
    $('.reject-btn').click(function() {
        const profileId = $(this).data('id');
        $('#profileId').val(profileId);
        $('#action').val('reject');
        $('.modal-title').text('Reject Club Profile');
        $('#notes').attr('placeholder', 'Add rejection reason (required)...');
        $('#moderationModal').modal('show');
    });

    // Handle moderation form submission
    $('#moderationForm').submit(function(e) {
        e.preventDefault();
        
        const profileId = $('#profileId').val();
        const action = $('#action').val();
        const notes = $('#notes').val();
        
        if (action === 'reject' && !notes.trim()) {
            alert('Please provide a reason for rejection.');
            return;
        }
        
        const url = action === 'approve' 
            ? `/admin/club-profiles/${profileId}/approve`
            : `/admin/club-profiles/${profileId}/reject`;
        
        $.ajax({
            url: url,
            method: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content'),
                notes: notes
            },
            success: function(response) {
                $('#moderationModal').modal('hide');
                location.reload();
            },
            error: function(xhr) {
                alert('Error: ' + (xhr.responseJSON?.message || 'Something went wrong'));
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/club-profiles/show.blade.php ENDPATH**/ ?>