<!DOCTYPE html>
<html lang="en" class="w-mod-js w-mod-ix">
<head>
  <meta charset="utf-8">
  <title>Catalog of Projects in the EVOLUTION888 Ecosystem</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <script src="<?php echo e(asset('js/gtranslate-helper.js')); ?>"></script>
  <style>
    /* Определение CSS-переменных для обеспечения видимости элементов */
    :root {
      --accent-color: #007bff;    /* Ярко-синий фон для кнопок */
      --border-color: #ccc;       /* Светло-серый цвет границы */
      --secondary-text: #666;     /* Темно-серый для второстепенного текста */
    }
    
    .news-list {
      list-style: none;
      padding: 0;
      margin: 20px auto 0; /* Отступ сверху, чтобы список был ниже заголовка и поиска */
      max-width: 800px;
    }
    .news-item {
      background-color: #f9f9f9;
      border: 1px solid var(--border-color);
      border-radius: 10px;
      padding: 15px;
      margin-bottom: 15px;
      transition: box-shadow 0.3s ease;
    }
    .news-item:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    /* Ссылка-обёртка для элемента */
    .news-item a {
      display: flex;
      text-decoration: none;
      color: inherit;
      width: 100%;
    }
    .news-image {
      width: 120px;
      height: 80px;
      object-fit: cover;
      border-radius: 8px;
      margin-right: 20px;
      flex-shrink: 0;
    }
    .news-content {
      flex: 1;
    }
    .news-title {
      font-size: 20px;
      margin: 0 0 10px;
    }
    .news-description {
      font-size: 16px;
      line-height: 1.4;
      color: var(--secondary-text);
      margin: 0;
    }
    .news-date {
      font-size: 14px;
      color: var(--secondary-text);
      margin-top: 10px;
    }
    /* Стили для пагинации */
    .pagination {
      text-align: center;
      margin: 30px 0;
    }
    .page-btn {
      padding: var(--components--buttons--paddings--pd-regular) var(--components--buttons--paddings--pd-medium);
      grid-column-gap: var(--components--buttons--gaps--gap-small);
      grid-row-gap: var(--components--buttons--gaps--gap-small);
      border: var(--components--buttons--border-width--bw-default) solid var(--components--buttons-primary--border-color--b-light-mode);
      border-radius: var(--components--buttons--border-radius--br-regular);
      background-color: var(--components--buttons-primary--backgrounds--bg-light-mode);
      box-shadow: 0 2px 4px 0 var(--core--box-shadow--bs-primary-regular);
      color: var(--components--buttons-primary--text--text-light-mode);
      font-size: var(--core--font-size--displays--display-2);
      line-height: var(--core--line-height--regular);
      text-align: center;
      transform-style: preserve-3d;
      justify-content: center;
      align-items: center;
      font-weight: 500;
      text-decoration: none;
      transition: transform .3s;
      cursor: pointer;
    }
    .page-btn:hover {
      color: var(--components--buttons-primary--text--text-light-mode);
      transform: scale3d(.94, .94, 1.01);
    }
    .page-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    #page-info {
      font-size: 16px;
      margin: 0 10px;
      vertical-align: middle;
    }
    
    /* Дополнительные стили, чтобы селект органично вписался в поиск */
    #search-wrapper {
      display: flex;
      align-items: center;
      gap: 5px; /* Расстояние между селектом и поисковым input */
    }
    #category-select {
      width: auto; 
      min-width: 110px; 
      padding: 0 6px; 
      /* можно корректировать, чтобы селект смотрелся компактно */
    }
  </style>
</head>
<body>
  <div class="page-wrapper">
    <!-- Верхняя навигация / хедер -->
    <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
  </div>

  <section class="section top small">
    <div class="w-container">
      <div data-w-id="0cdb6d37-381d-7c43-57fb-24882cc1868b" style="transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg); opacity: 1; transform-style: preserve-3d;" class="title-left---content-right center---mbl">
        <div class="width-100-mbl">
          <h2 class="display-8 mid">All projects</h2>
        </div>
        <!-- Вместо формы делаем обычный контейнер (сохраняем "старые" классы поиска) -->
        <div class="position-relative mg-bottom-0 w-form" id="search-wrapper">
          <!-- Селект категории, встроен в дизайн поиска -->
          <select id="category-select" class="input button-inside icon-inside w-input">
            <option value="all">Categories</option>
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <option value="<?php echo e($cat); ?>"><?php echo e($cat); ?></option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </select>

          <!-- Исходный инпут поиска (не меняем классы) -->
          <label for="search" class="hidden">Search</label>
          <input 
            class="input button-inside icon-inside w-input" 
            maxlength="256" 
            name="query" 
            placeholder="Search project…" 
            type="search" 
            id="search" 
            required
          >

          <!-- Сохраняем "кнопку" с иконкой, чтобы не ломать дизайн. 
               Но при любом изменении делаем автопоиск, поэтому по клику будем просто блокировать перезагрузку. -->
          <input 
            type="submit" 
            class="primary-button inside-input button-icon w-button" 
            value="" 
            id="search-btn"
          >
        </div>
      </div>
      <!-- Список элементов (UL) -->
      <ul class="news-list" id="data-list">
        <!-- Данные подгружаются через AJAX -->
      </ul>
      <!-- Пагинация -->
      <div class="pagination" id="news-pagination">
        <button id="prev-page-btn" class="page-btn" style="display: none;">← Back</button>
        <span id="page-info"></span>
        <button id="next-page-btn" class="page-btn" style="display: none;">Next →</button>
      </div>
    </div>
  </section>

  <footer>
    <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
  </footer>

  <!-- Скрипты -->
  <script
    src="js/jquery-3.5.1.min.dc5e7f18c8.js"
    type="text/javascript"
    integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="
    crossorigin="anonymous">
  </script>
  <script src="js/webflow.3de03aa26.js" type="text/javascript"></script>
  <script type="text/javascript">
    // Функция форматирования даты
    function formatDateTime(datetimeStr) {
      const dateObj = new Date(datetimeStr);
      if (isNaN(dateObj.getTime())) return datetimeStr;
      return dateObj.toLocaleString([], { 
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
    
    $(document).ready(function () {
      let currentPage = 1;
      let totalPages = 1;

      // Основная функция AJAX-запроса
      function fetchData(page = 1) {
        const searchValue   = $('#search').val().trim();
        const categoryValue = $('#category-select').val() || 'all';

        $.ajax({
          url: "/projects_search",
          method: "GET",
          data: {
            page: page,
            search: searchValue,
            category: categoryValue
          },
          success: function (response) {
            $("#data-list").empty();

            const items = response.data || [];
            totalPages  = response.meta ? response.meta.total_pages : 1;
            currentPage = response.meta ? response.meta.current_page : 1;

            // Рендерим элементы
            items.forEach(function (item) {
              const itemHtml = `
                <li class="news-item">
                  <a href="/project_view?id=${item._id}">
                    <img src="${item.img}" alt="${item.name}" class="news-image">
                    <div class="news-content">
                      <h3 class="news-title">${item.name}</h3>
                      <div class="project-category">Category: ${item.category}</div>
                    </div>
                  </a>
                </li>
              `;
              $("#data-list").append(itemHtml);
            });

            updatePagination();
            
            // Запускаем перевод для нового контента
            if (window.GTranslateHelper) {
              setTimeout(() => {
                window.GTranslateHelper.handleDynamicContent(document.getElementById('data-list'));
              }, 500);
            }
          },
          error: function (err) {
            console.error("Ошибка при получении данных:", err);
          }
        });
      }

      // Обновление пагинации
      function updatePagination() {
        $("#page-info").text("Page " + currentPage + " of " + totalPages);

        if (currentPage > 1) {
          $("#prev-page-btn").show();
        } else {
          $("#prev-page-btn").hide();
        }
        if (currentPage < totalPages) {
          $("#next-page-btn").show();
        } else {
          $("#next-page-btn").hide();
        }
      }

      // События изменения / ввода (автопоиск без клика)
      $('#search').on('input', function() {
        // При каждом вводе текста сбрасываем страницу на 1
        currentPage = 1;
        fetchData(currentPage);
      });
      $('#category-select').on('change', function() {
        // При смене категории тоже сбрасываем страницу на 1
        currentPage = 1;
        fetchData(currentPage);
      });

      // Блокируем действие "кнопки-лупы", чтобы не перезагружала страницу
      $('#search-btn').on('click', function(e) {
        e.preventDefault();
      });

      // Пагинация
      $("#prev-page-btn").click(function () {
        if (currentPage > 1) {
          currentPage--;
          fetchData(currentPage);
        }
      });
      $("#next-page-btn").click(function () {
        if (currentPage < totalPages) {
          currentPage++;
          fetchData(currentPage);
        }
      });

      // Первичная загрузка
      fetchData(currentPage);
    });
  </script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/project_list.blade.php ENDPATH**/ ?>