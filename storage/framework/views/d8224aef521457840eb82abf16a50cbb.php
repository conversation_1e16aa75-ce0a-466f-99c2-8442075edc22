<!DOCTYPE html>
<html data-wf-domain="/" data-wf-page="663d095d8e07618c5381377b" data-wf-site="663b748d327c826a2952af46" data-wf-status="1" lang="en" class=" w-mod-js w-mod-ix">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>My Business Cards - EVOLUTION888</title>

  <!-- CSRF Token (Laravel) -->
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
  <!-- Main CSS -->
  <link rel="stylesheet" href="<?php echo e(asset('css/style.css')); ?>" />
  <link href="css/techbetatemplates.webflow.14600f26e.css?v=0.3" rel="stylesheet" type="text/css">

  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

  <style>
    .business-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
      transition: transform 0.2s, box-shadow 0.2s;
    }
    .business-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    .card-logo {
      width: 60px;
      height: 60px;
      border-radius: 8px;
      object-fit: cover;
    }
    .card-actions {
      display: flex;
      gap: 10px;
    }
    .btn-sm {
      padding: 5px 12px;
      font-size: 12px;
      border-radius: 6px;
      text-decoration: none;
      display: inline-block;
      transition: all 0.2s;
    }
    .btn-primary { background: #007bff; color: white; border: none; }
    .btn-success { background: #28a745; color: white; border: none; }
    .btn-warning { background: #ffc107; color: #212529; border: none; }
    .btn-danger { background: #dc3545; color: white; border: none; }
    .btn-sm:hover { opacity: 0.8; }
    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: bold;
    }
    .status-active { background: #d4edda; color: #155724; }
    .status-inactive { background: #f8d7da; color: #721c24; }
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 15px;
      margin-top: 15px;
    }
    .stat-item {
      text-align: center;
      padding: 10px;
      background: #f8f9fa;
      border-radius: 8px;
    }
    .stat-number {
      font-size: 18px;
      font-weight: bold;
      color: #007bff;
    }
    .stat-label {
      font-size: 12px;
      color: #6c757d;
      margin-top: 4px;
    }
  </style>
</head>

<body>
  <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

  <div class="page-wrapper">
    <section class="section template-pages-hero" style="margin-top: -120px;">
      <div class="w-layout-blockcontainer container-default w-container">
        <div class="card template-hero-card">
          <div class="inner-container _640px center">
            <div class="text-center">
              <h1 class="display-9 mid">My Business Cards</h1>
              <div class="mg-top-extra-small">
                <p class="paragraph-large">Manage your digital business cards</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="section">
      <div class="w-layout-blockcontainer container-default w-container">
        
        <!-- Action Bar -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
          <div>
            <h2>Your Business Cards (<?php echo e($businessCards->count()); ?>)</h2>
          </div>
          <div>
            <a href="<?php echo e(route('business-cards.create')); ?>" class="btn-primary btn-sm">
              <i class="fas fa-plus"></i> Create New Card
            </a>
          </div>
        </div>

        <!-- Business Cards Grid -->
        <?php if($businessCards->count() > 0): ?>
          <div class="w-layout-grid" style="grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px;">
            <?php $__currentLoopData = $businessCards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $card): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <div class="business-card">
                <div class="card-header">
                  <div style="display: flex; align-items: center; gap: 15px;">
                    <?php if($card->logo): ?>
                      <img src="<?php echo e($card->logo); ?>" alt="Logo" class="card-logo">
                    <?php else: ?>
                      <div class="card-logo" style="background: #f8f9fa; display: flex; align-items: center; justify-content: center; color: #6c757d;">
                        <i class="fas fa-image"></i>
                      </div>
                    <?php endif; ?>
                    <div>
                      <h3 style="margin: 0; font-size: 18px;"><?php echo e($card->title); ?></h3>
                      <p style="margin: 5px 0 0 0; color: #6c757d; font-size: 14px;">
                        /tag/<?php echo e($card->profile_name); ?>

                      </p>
                    </div>
                  </div>
                  <div>
                    <span class="status-badge <?php echo e($card->is_active ? 'status-active' : 'status-inactive'); ?>">
                      <?php echo e($card->is_active ? 'Active' : 'Inactive'); ?>

                    </span>
                  </div>
                </div>

                <?php if($card->description): ?>
                  <p style="color: #6c757d; margin-bottom: 15px; font-size: 14px;">
                    <?php echo e(Str::limit($card->description, 100)); ?>

                  </p>
                <?php endif; ?>

                <!-- Stats -->
                <div class="stats-grid">
                  <div class="stat-item">
                    <div class="stat-number"><?php echo e($card->views_count); ?></div>
                    <div class="stat-label">Views</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number"><?php echo e($card->contacts ? count($card->contacts) : 0); ?></div>
                    <div class="stat-label">Contacts</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number"><?php echo e($card->created_at->format('M d')); ?></div>
                    <div class="stat-label">Created</div>
                  </div>
                </div>

                <!-- Actions -->
                <div class="card-actions" style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e9ecef;">
                  <a href="<?php echo e($card->public_url); ?>" target="_blank" class="btn-primary btn-sm">
                    <i class="fas fa-eye"></i> View
                  </a>
                  <a href="<?php echo e(route('business-cards.edit', $card->id)); ?>" class="btn-warning btn-sm">
                    <i class="fas fa-edit"></i> Edit
                  </a>
                  <button onclick="toggleActive('<?php echo e($card->id); ?>', <?php echo e($card->is_active ? 'false' : 'true'); ?>)" 
                          class="btn-success btn-sm">
                    <i class="fas fa-<?php echo e($card->is_active ? 'pause' : 'play'); ?>"></i> 
                    <?php echo e($card->is_active ? 'Deactivate' : 'Activate'); ?>

                  </button>
                  <button onclick="deleteCard('<?php echo e($card->id); ?>')" class="btn-danger btn-sm">
                    <i class="fas fa-trash"></i> Delete
                  </button>
                </div>
              </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </div>
        <?php else: ?>
          <!-- Empty State -->
          <div style="text-align: center; padding: 60px 20px;">
            <div style="font-size: 48px; color: #dee2e6; margin-bottom: 20px;">
              <i class="fas fa-id-card"></i>
            </div>
            <h3 style="color: #6c757d; margin-bottom: 10px;">No Business Cards Yet</h3>
            <p style="color: #6c757d; margin-bottom: 30px;">Create your first digital business card to get started.</p>
            <a href="<?php echo e(route('business-cards.create')); ?>" class="btn-primary btn-sm" style="padding: 12px 24px; font-size: 14px;">
              <i class="fas fa-plus"></i> Create Your First Card
            </a>
          </div>
        <?php endif; ?>

      </div>
    </section>
  </div>

  <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

  <script>
    function toggleActive(cardId, newStatus) {
      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
      });

      $.post(`/business-cards/${cardId}/toggle-active`, {
        is_active: newStatus
      })
      .done(function(response) {
        if (response.status === 'success') {
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: response.message,
            timer: 2000,
            showConfirmButton: false
          }).then(() => {
            location.reload();
          });
        }
      })
      .fail(function(xhr) {
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: xhr.responseJSON?.message || 'An error occurred'
        });
      });
    }

    function deleteCard(cardId) {
      Swal.fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, delete it!'
      }).then((result) => {
        if (result.isConfirmed) {
          $.ajaxSetup({
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
          });

          $.ajax({
            url: `/business-cards/${cardId}`,
            type: 'DELETE',
            success: function(response) {
              if (response.status === 'success') {
                Swal.fire({
                  icon: 'success',
                  title: 'Deleted!',
                  text: response.message,
                  timer: 2000,
                  showConfirmButton: false
                }).then(() => {
                  location.reload();
                });
              }
            },
            error: function(xhr) {
              Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: xhr.responseJSON?.message || 'An error occurred'
              });
            }
          });
        }
      });
    }
  </script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/business-cards/index.blade.php ENDPATH**/ ?>