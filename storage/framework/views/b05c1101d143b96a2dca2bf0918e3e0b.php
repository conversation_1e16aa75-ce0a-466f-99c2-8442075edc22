<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Two-Factor Authentication Setup</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css">
</head>
<body>
    <div class="min-h-screen flex items-center justify-center bg-gray-100">
        <div class="bg-white p-8 rounded shadow-md w-full max-w-md">
            <h2 class="text-2xl font-bold mb-6 text-center">Two-Factor Authentication</h2>

            <!-- Success message -->
            <?php if(session('success')): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <!-- Error message -->
            <?php if($errors->any()): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
                    <?php echo e($errors->first()); ?>

                </div>
            <?php endif; ?>

            <?php if($user->two_factor_secret): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
                    Two-Factor Authentication is already enabled.
                </div>

                <!-- Form to deactivate 2FA -->
                <form action="<?php echo e(route('2fa.deactivate')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full">
                        Disable 2FA
                    </button>
                </form>
            <?php else: ?>
                <p class="text-center mb-4">Scan the QR code below with your authenticator app to enable 2FA.</p>
                <div class="flex justify-center mb-4">
                    <img src="<?php echo e($qrCodeImage); ?>" alt="QR Code">
                </div>

                <p class="text-center mb-4">Your 2FA secret: <strong><?php echo e($secret); ?></strong></p>

                <p class="text-center text-gray-600 mb-4">After scanning the code, you can use your authenticator app to generate a one-time code for login.</p>

                <!-- Form for entering the verification code -->
                <form action="<?php echo e(route('2fa.activate.submit')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="secret" value="<?php echo e($secret); ?>">
                    <div class="mb-4">
                        <label for="code" class="block text-gray-700 font-bold mb-2">Enter Code from App:</label>
                        <input type="text" name="code" id="code" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    </div>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full">
                        Verify & Enable 2FA
                    </button>
                </form>
            <?php endif; ?>

            <!-- Back to Dashboard link -->
            <a href="<?php echo e(route('dashboard')); ?>" class="block text-center bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mt-4">
                Back to Dashboard
            </a>
        </div>
    </div>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/auth/2fa_activate.blade.php ENDPATH**/ ?>