<?php $__env->startSection('title'); ?>
Edit Business Card - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
    <style>
        .card-logo-preview {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .form-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>

<!-- Page Title Area Start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Edit Business Card</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><a href="<?php echo e(route('admin.business-cards.index')); ?>">Business Cards</a></li>
                    <li><span>Edit</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- Page Title Area End -->

<div class="main-content-inner">
    <div class="row">
        <div class="col-lg-8 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-4">Edit Business Card</h4>
                    
                    <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    
                    <form method="POST" action="<?php echo e(route('admin.business-cards.update', $card->_id)); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <!-- Basic Information -->
                        <div class="form-section">
                            <h5 class="mb-3">Basic Information</h5>
                            
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <label for="title">Title <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="title" name="title" value="<?php echo e(old('title', $card->title)); ?>" required>
                                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <?php if($card->logo): ?>
                                        <label>Current Logo</label><br>
                                        <img src="<?php echo e(Storage::url($card->logo)); ?>" alt="Logo" class="card-logo-preview">
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="description">Description</label>
                                <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                          id="description" name="description" rows="3" 
                                          maxlength="1000"><?php echo e(old('description', $card->description)); ?></textarea>
                                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <small class="form-text text-muted">Maximum 1000 characters</small>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Profile Name</label>
                                        <input type="text" class="form-control" value="<?php echo e($card->profile_name); ?>" readonly>
                                        <small class="form-text text-muted">Profile name cannot be changed</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Public URL</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" value="<?php echo e($card->public_url); ?>" readonly>
                                            <div class="input-group-append">
                                                <a href="<?php echo e($card->public_url); ?>" target="_blank" class="btn btn-outline-secondary">
                                                    <i class="fa fa-external-link"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Status & Moderation -->
                        <div class="form-section">
                            <h5 class="mb-3">Status & Moderation</h5>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="is_active" 
                                                   name="is_active" <?php echo e(old('is_active', $card->is_active) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="is_active">
                                                Active
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="moderation_status">Moderation Status <span class="text-danger">*</span></label>
                                        <select class="form-control <?php $__errorArgs = ['moderation_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="moderation_status" name="moderation_status" required>
                                            <option value="pending" <?php echo e(old('moderation_status', $card->moderation_status) == 'pending' ? 'selected' : ''); ?>>
                                                Pending
                                            </option>
                                            <option value="approved" <?php echo e(old('moderation_status', $card->moderation_status) == 'approved' ? 'selected' : ''); ?>>
                                                Approved
                                            </option>
                                            <option value="rejected" <?php echo e(old('moderation_status', $card->moderation_status) == 'rejected' ? 'selected' : ''); ?>>
                                                Rejected
                                            </option>
                                        </select>
                                        <?php $__errorArgs = ['moderation_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Views Count</label>
                                        <input type="text" class="form-control" value="<?php echo e(number_format($card->views_count)); ?>" readonly>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="moderation_notes">Moderation Notes</label>
                                <textarea class="form-control <?php $__errorArgs = ['moderation_notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                          id="moderation_notes" name="moderation_notes" rows="3" 
                                          maxlength="500"><?php echo e(old('moderation_notes', $card->moderation_notes)); ?></textarea>
                                <?php $__errorArgs = ['moderation_notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <small class="form-text text-muted">Maximum 500 characters</small>
                            </div>
                        </div>
                        
                        <!-- User Information (Read-only) -->
                        <div class="form-section">
                            <h5 class="mb-3">User Information</h5>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>User Name</label>
                                        <input type="text" class="form-control" value="<?php echo e($card->user->name ?? 'N/A'); ?>" readonly>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>User Email</label>
                                        <input type="text" class="form-control" value="<?php echo e($card->user->email ?? 'N/A'); ?>" readonly>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Created Date</label>
                                        <input type="text" class="form-control" value="<?php echo e($card->created_at->format('M d, Y H:i')); ?>" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Contact Information (Read-only) -->
                        <?php if($card->contacts && count($card->contacts) > 0): ?>
                        <div class="form-section">
                            <h5 class="mb-3">Contact Information</h5>
                            
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Type</th>
                                            <th>Label</th>
                                            <th>Value</th>
                                            <th>Color</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $card->contacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e(ucfirst($contact['type'] ?? 'link')); ?></td>
                                            <td><?php echo e($contact['label'] ?? '-'); ?></td>
                                            <td><?php echo e($contact['value'] ?? '-'); ?></td>
                                            <td>
                                                <span class="badge" style="background-color: <?php echo e($contact['color'] ?? '#007bff'); ?>;">
                                                    <?php echo e($contact['color'] ?? '#007bff'); ?>

                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Form Actions -->
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-save"></i> Update Business Card
                            </button>
                            <a href="<?php echo e(route('admin.business-cards.show', $card->_id)); ?>" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> Back to Details
                            </a>
                            <a href="<?php echo e(route('admin.business-cards.index')); ?>" class="btn btn-outline-secondary">
                                <i class="fa fa-list"></i> Back to List
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions Sidebar -->
        <div class="col-lg-4 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Quick Actions</h4>
                    
                    <div class="d-grid gap-2">
                        <a href="<?php echo e($card->public_url); ?>" target="_blank" class="btn btn-info btn-block">
                            <i class="fa fa-external-link"></i> View Public Card
                        </a>
                        
                        <?php if($card->isPending()): ?>
                            <button class="btn btn-success btn-block approve-btn" data-id="<?php echo e($card->_id); ?>">
                                <i class="fa fa-check"></i> Quick Approve
                            </button>
                            <button class="btn btn-warning btn-block reject-btn" data-id="<?php echo e($card->_id); ?>">
                                <i class="fa fa-times"></i> Quick Reject
                            </button>
                        <?php endif; ?>
                        
                        <button class="btn btn-danger btn-block delete-btn" data-id="<?php echo e($card->_id); ?>">
                            <i class="fa fa-trash"></i> Delete Card
                        </button>
                    </div>
                    
                    <?php if($card->moderated_at): ?>
                        <hr>
                        <h6>Last Moderation</h6>
                        <p class="small text-muted mb-1">
                            <strong>Date:</strong> <?php echo e($card->moderated_at->format('M d, Y H:i')); ?>

                        </p>
                        <?php if($card->moderator): ?>
                            <p class="small text-muted mb-1">
                                <strong>By:</strong> <?php echo e($card->moderator->name); ?>

                            </p>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Quick approve
            $('.approve-btn').click(function() {
                if (confirm('Are you sure you want to approve this business card?')) {
                    const cardId = $(this).data('id');
                    $.post(`/admin/business-cards/${cardId}/approve`, {
                        _token: '<?php echo e(csrf_token()); ?>'
                    }).done(function(response) {
                        if (response.success) {
                            location.reload();
                        }
                    });
                }
            });

            // Quick reject
            $('.reject-btn').click(function() {
                const reason = prompt('Please enter rejection reason:');
                if (reason && reason.trim()) {
                    const cardId = $(this).data('id');
                    $.post(`/admin/business-cards/${cardId}/reject`, {
                        _token: '<?php echo e(csrf_token()); ?>',
                        notes: reason
                    }).done(function(response) {
                        if (response.success) {
                            location.reload();
                        }
                    });
                }
            });

            // Delete button
            $('.delete-btn').click(function() {
                if (confirm('Are you sure you want to delete this business card? This action cannot be undone.')) {
                    const cardId = $(this).data('id');
                    const form = $('<form method="POST" action="/admin/business-cards/' + cardId + '">' +
                        '<input type="hidden" name="_token" value="<?php echo e(csrf_token()); ?>">' +
                        '<input type="hidden" name="_method" value="DELETE">' +
                        '</form>');
                    $('body').append(form);
                    form.submit();
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/business-cards/edit.blade.php ENDPATH**/ ?>