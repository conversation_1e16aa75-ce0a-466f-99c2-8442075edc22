<?php $__env->startSection('title'); ?>
    Edit Club Profile - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
    <style>
        .profile-banner {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        .tag-item {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            margin: 2px;
        }
        .form-help {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-active { background: #28a745; color: white; }
        .status-inactive { background: #6c757d; color: white; }
        .status-pending { background: #ffc107; color: #212529; }
        .status-approved { background: #28a745; color: white; }
        .status-rejected { background: #dc3545; color: white; }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>

<!-- Page Title Area Start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Edit Club Profile</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><a href="<?php echo e(route('admin.club-profiles.index')); ?>">Club Profiles</a></li>
                    <li><span>Edit</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- Page Title Area End -->

<div class="main-content-inner">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Edit Club Profile</h4>
                    
                    <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    
                    <form method="POST" action="<?php echo e(route('admin.club-profiles.update', $profile->_id)); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <div class="form-group">
                            <label for="profile_name">Profile Name (URL)</label>
                            <input type="text" class="form-control" id="profile_name" name="profile_name" 
                                   value="<?php echo e(old('profile_name', $profile->profile_name)); ?>" readonly>
                            <div class="form-help">Profile URL cannot be changed</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="display_name">Display Name</label>
                            <input type="text" class="form-control" id="display_name" name="display_name" 
                                   value="<?php echo e(old('display_name', $profile->display_name)); ?>" readonly>
                            <div class="form-help">Display name is managed by the user</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="5"><?php echo e(old('description', $profile->description)); ?></textarea>
                            <div class="form-help">Maximum 2000 characters</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="tags">Tags</label>
                            <input type="text" class="form-control" id="tags" name="tags" 
                                   value="<?php echo e(old('tags', is_array($profile->tags) ? implode(', ', $profile->tags) : '')); ?>">
                            <div class="form-help">Separate tags with commas (e.g., business, technology, innovation)</div>
                        </div>
                        
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                       <?php echo e(old('is_active', $profile->is_active) ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_active">
                                    Active Status
                                </label>
                            </div>
                            <div class="form-help">Inactive profiles are not visible to the public</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="moderation_status">Moderation Status</label>
                            <select class="form-control" id="moderation_status" name="moderation_status" required>
                                <option value="pending" <?php echo e(old('moderation_status', $profile->moderation_status) == 'pending' ? 'selected' : ''); ?>>
                                    Pending Review
                                </option>
                                <option value="approved" <?php echo e(old('moderation_status', $profile->moderation_status) == 'approved' ? 'selected' : ''); ?>>
                                    Approved
                                </option>
                                <option value="rejected" <?php echo e(old('moderation_status', $profile->moderation_status) == 'rejected' ? 'selected' : ''); ?>>
                                    Rejected
                                </option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="moderation_notes">Moderation Notes</label>
                            <textarea class="form-control" id="moderation_notes" name="moderation_notes" rows="3"><?php echo e(old('moderation_notes', $profile->moderation_notes)); ?></textarea>
                            <div class="form-help">Internal notes about moderation decision (maximum 500 characters)</div>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-save"></i> Update Profile
                            </button>
                            <a href="<?php echo e(route('admin.club-profiles.show', $profile->_id)); ?>" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> Back to Profile
                            </a>
                            <a href="<?php echo e(route('admin.club-profiles.index')); ?>" class="btn btn-info">
                                <i class="fa fa-list"></i> All Profiles
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Current Status -->
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Current Status</h4>
                    
                    <div class="mb-3">
                        <strong>Status:</strong>
                        <span class="status-badge status-<?php echo e($profile->is_active ? 'active' : 'inactive'); ?>">
                            <?php echo e($profile->is_active ? 'Active' : 'Inactive'); ?>

                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Moderation:</strong>
                        <span class="status-badge status-<?php echo e($profile->moderation_status); ?>">
                            <?php echo e(ucfirst($profile->moderation_status)); ?>

                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Views:</strong> <?php echo e(number_format($profile->views_count)); ?>

                    </div>
                    
                    <div class="mb-3">
                        <strong>Created:</strong> <?php echo e($profile->created_at->format('M d, Y')); ?>

                    </div>
                    
                    <div class="mb-3">
                        <strong>Last Updated:</strong> <?php echo e($profile->updated_at->format('M d, Y')); ?>

                    </div>
                    
                    <?php if($profile->moderated_at): ?>
                        <div class="mb-3">
                            <strong>Last Moderated:</strong> <?php echo e($profile->moderated_at->format('M d, Y')); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- User Information -->
            <div class="card mt-4">
                <div class="card-body">
                    <h4 class="header-title">Profile Owner</h4>
                    
                    <div class="mb-3">
                        <strong>Name:</strong> <?php echo e($profile->user->full_name ?? $profile->user->name ?? 'N/A'); ?>

                    </div>
                    
                    <div class="mb-3">
                        <strong>Email:</strong> <?php echo e($profile->user->email); ?>

                    </div>
                    
                    <div class="mb-3">
                        <strong>User ID:</strong> <?php echo e($profile->user->_id); ?>

                    </div>
                    
                    <div class="mb-3">
                        <strong>Joined:</strong> <?php echo e($profile->user->created_at->format('M d, Y')); ?>

                    </div>
                </div>
            </div>
            
            <!-- Preview -->
            <div class="card mt-4">
                <div class="card-body">
                    <h4 class="header-title">Quick Actions</h4>
                    
                    <a href="<?php echo e(route('club.profile.public', $profile->profile_name)); ?>" target="_blank" class="btn btn-info btn-block mb-2">
                        <i class="fa fa-eye"></i> View Public Profile
                    </a>
                    
                    <?php if($profile->banner_image): ?>
                        <div class="mb-3">
                            <strong>Current Banner:</strong>
                            <img src="<?php echo e($profile->banner_image); ?>" alt="Profile Banner" class="profile-banner mt-2">
                        </div>
                    <?php endif; ?>
                    
                    <?php if($profile->tags && count($profile->tags) > 0): ?>
                        <div class="mb-3">
                            <strong>Current Tags:</strong><br>
                            <?php $__currentLoopData = $profile->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <span class="tag-item"><?php echo e($tag); ?></span>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Character counter for description
    $('#description').on('input', function() {
        const maxLength = 2000;
        const currentLength = $(this).val().length;
        const remaining = maxLength - currentLength;
        
        if (!$('#desc-counter').length) {
            $(this).after('<div id="desc-counter" class="form-help"></div>');
        }
        
        $('#desc-counter').text(`${currentLength}/${maxLength} characters`);
        
        if (remaining < 0) {
            $('#desc-counter').addClass('text-danger');
        } else {
            $('#desc-counter').removeClass('text-danger');
        }
    });
    
    // Character counter for moderation notes
    $('#moderation_notes').on('input', function() {
        const maxLength = 500;
        const currentLength = $(this).val().length;
        const remaining = maxLength - currentLength;
        
        if (!$('#notes-counter').length) {
            $(this).after('<div id="notes-counter" class="form-help"></div>');
        }
        
        $('#notes-counter').text(`${currentLength}/${maxLength} characters`);
        
        if (remaining < 0) {
            $('#notes-counter').addClass('text-danger');
        } else {
            $('#notes-counter').removeClass('text-danger');
        }
    });
    
    // Trigger counters on page load
    $('#description').trigger('input');
    $('#moderation_notes').trigger('input');
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/club-profiles/edit.blade.php ENDPATH**/ ?>