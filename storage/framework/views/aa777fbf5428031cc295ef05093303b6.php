<?php $__env->startSection('title'); ?>
PaymentMethod Edit - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
<!-- Если Summernote не требуется, его можно удалить -->
<style>
    .form-check-label {
        text-transform: capitalize;
    }
    .image-preview {
        width: 100px;
        height: 100px;
        border: 1px solid #ddd;
        margin-top: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }
    .image-preview img {
        width: 100%;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>
<!-- Page Title Area Start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Edit PaymentMethod</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><a href="<?php echo e(route('admin.payment_methods.index')); ?>">All PaymentMethods</a></li>
                    <li><span>Edit PaymentMethod - <?php echo e($payment_method->name); ?></span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- Page Title Area End -->

<div class="main-content-inner">
    <div class="row">
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Edit PaymentMethod - <?php echo e($payment_method->name); ?></h4>
                    <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <form action="<?php echo e(route('admin.payment_methods.update', $payment_method->id)); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo method_field('PUT'); ?>
                        <?php echo csrf_field(); ?>
                        <!-- Name Field -->
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="name">Name</label>
                                <input type="text" class="form-control" id="name" name="name" placeholder="Enter Name" value="<?php echo e($payment_method->name); ?>" required>
                            </div>
                        </div>
                        <!-- Example Address Field -->
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="example_address">Example Address</label>
                                <input type="text" class="form-control" id="example_address" name="example_address" placeholder="Enter Example Address" value="<?php echo e($payment_method->example_address); ?>" required>
                            </div>
                        </div>
                        <!-- Minimum Amount Field -->
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="min_amount">Minimum Amount</label>
                                <input type="number" class="form-control" id="min_amount" name="min_amount" placeholder="Enter Minimum Amount" value="<?php echo e($payment_method->min_amount); ?>" required min="0" step="0.01">
                            </div>
                        </div>
                        <!-- Image Upload Field -->
                        <div class="form-row">
                            <div class="form-group col-md-12">
                                <label for="img">PaymentMethod Image</label>
                                <input type="file" class="form-control" id="img" name="img" onchange="previewImage();">
                                <div class="image-preview" id="imagePreview">
                                    <?php if($payment_method->img): ?>
                                        <img src="<?php echo e($payment_method->img); ?>" alt="PaymentMethod Image">
                                    <?php else: ?>
                                        <span>No image</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary mt-4 pr-4 pl-4">Update PaymentMethod</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2();
    });

    function previewImage() {
        const file = document.querySelector('#img').files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const imgElement = document.createElement('img');
                imgElement.src = e.target.result;
                document.querySelector('#imagePreview').innerHTML = '';
                document.querySelector('#imagePreview').appendChild(imgElement);
            };
            reader.readAsDataURL(file);
        } else {
            document.querySelector('#imagePreview').innerHTML = '<span>No image</span>';
        }
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/payment_methods/edit.blade.php ENDPATH**/ ?>