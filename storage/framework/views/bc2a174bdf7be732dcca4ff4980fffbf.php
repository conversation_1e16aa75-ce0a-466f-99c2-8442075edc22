<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><?php echo e($businessCard->title); ?> - Business Card</title>
  
  <!-- Meta tags for social sharing -->
  <meta property="og:title" content="<?php echo e($businessCard->title); ?>">
  <meta property="og:description" content="<?php echo e($businessCard->description ?? 'Digital Business Card'); ?>">
  <meta property="og:type" content="profile">
  <meta property="og:url" content="<?php echo e($businessCard->public_url); ?>">
  <?php if($businessCard->logo): ?>
    <meta property="og:image" content="<?php echo e($businessCard->logo); ?>">
  <?php endif; ?>
  
  <!-- Main CSS -->
  <link rel="stylesheet" href="<?php echo e(asset('css/style.css')); ?>" />
  <link href="css/techbetatemplates.webflow.14600f26e.css?v=0.3" rel="stylesheet" type="text/css">
  
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  
  <style>
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      font-family: 'Arial', sans-serif;
      margin: 0;
      padding: 20px;
    }
    
    .business-card-container {
      max-width: 400px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      animation: slideUp 0.6s ease-out;
    }
    
    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    .card-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 30px 20px;
      text-align: center;
      color: white;
    }
    
    .card-logo {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      object-fit: cover;
      border: 4px solid white;
      margin-bottom: 15px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .card-logo-placeholder {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 15px;
      font-size: 32px;
      color: white;
    }
    
    .card-title {
      font-size: 24px;
      font-weight: bold;
      margin: 0 0 8px 0;
    }
    
    .card-url {
      font-size: 14px;
      opacity: 0.9;
      margin: 0;
    }
    
    .card-body {
      padding: 30px 20px;
    }
    
    .card-description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 30px;
      text-align: center;
    }
    
    .contacts-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 12px;
    }
    
    .contact-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      background: #f8f9fa;
      border-radius: 12px;
      text-decoration: none;
      color: #333;
      transition: all 0.3s ease;
      border-left: 4px solid transparent;
    }
    
    .contact-item:hover {
      background: #e9ecef;
      transform: translateX(4px);
      text-decoration: none;
      color: #333;
    }
    
    .contact-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      color: white;
      font-size: 16px;
    }
    
    .contact-info {
      flex: 1;
    }
    
    .contact-label {
      font-weight: 600;
      font-size: 14px;
      margin-bottom: 2px;
    }
    
    .contact-value {
      font-size: 13px;
      color: #666;
      word-break: break-all;
      word-wrap: break-word;
      overflow-wrap: break-word;
      hyphens: auto;
      line-height: 1.3;
    }

    /* Специальные стили для email */
    .contact-value.email-value {
      font-size: 12px;
      max-width: 100%;
      display: block;
    }

    /* Для очень длинных email - показываем сокращенную версию */
    .contact-value.long-email {
      position: relative;
    }

    .contact-value.long-email .email-full {
      display: none;
    }

    .contact-value.long-email .email-short {
      display: block;
    }

    .contact-value.long-email:hover .email-full {
      display: block;
      position: absolute;
      background: #333;
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      z-index: 1000;
      top: -40px;
      left: 0;
      white-space: nowrap;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      font-size: 12px;
      max-width: 300px;
      word-break: break-all;
    }

    .contact-value.long-email:hover .email-short {
      display: none;
    }
    
    .qr-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 20px;
      border-top: 1px solid #e9ecef;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      position: relative;
    }

    .qr-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 50px;
      height: 3px;
      background: linear-gradient(90deg, #667eea, #764ba2);
      border-radius: 0 0 3px 3px;
    }
    
    .qr-container {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 15px;
      background: white;
      border-radius: 15px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      position: relative;
      margin: 0 auto;
      width: fit-content;
    }

    .qr-container:hover {
      transform: scale(1.05);
      box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
    }

    .qr-code {
      display: block;
      width: 120px;
      height: 120px;
      border-radius: 8px;
      background: white;
      margin: 0 auto;
    }
    
    .qr-label {
      font-size: 12px;
      color: #666;
      margin-top: 12px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      position: relative;
    }

    .qr-label::before {
      content: '📱';
      margin-right: 5px;
      font-size: 14px;
    }
    
    .powered-by {
      text-align: center;
      padding: 15px;
      font-size: 11px;
      color: #999;
      background: #f8f9fa;
    }
    
    .powered-by a {
      color: #667eea;
      text-decoration: none;
    }
    
    @media (max-width: 480px) {
      body {
        padding: 10px;
      }
      
      .business-card-container {
        margin: 10px auto;
      }
      
      .card-header {
        padding: 25px 15px;
      }
      
      .card-body {
        padding: 25px 15px;
      }

      .qr-container {
        padding: 10px;
      }

      .qr-code {
        width: 100px;
        height: 100px;
      }

      .qr-section {
        padding: 15px;
      }
    }
  </style>
</head>

<body>
  <div class="business-card-container">
    <!-- Card Header -->
    <div class="card-header">
      <?php if($businessCard->logo): ?>
        <img src="<?php echo e($businessCard->logo); ?>" alt="<?php echo e($businessCard->title); ?>" class="card-logo">
      <?php else: ?>
        <div class="card-logo-placeholder">
          <i class="fas fa-user"></i>
        </div>
      <?php endif; ?>
      
      <h1 class="card-title"><?php echo e($businessCard->title); ?></h1>
      <p class="card-url"><?php echo e(url('/tag/' . $businessCard->profile_name)); ?></p>
    </div>

    <!-- Card Body -->
    <div class="card-body">
      <?php if($businessCard->description): ?>
        <div class="card-description">
          <?php echo e($businessCard->description); ?>

        </div>
      <?php endif; ?>

      <!-- Contacts -->
      <?php if($businessCard->formatted_contacts && count($businessCard->formatted_contacts) > 0): ?>
        <div class="contacts-grid">
          <?php $__currentLoopData = $businessCard->formatted_contacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
              $href = '#';
              $target = '';
              
              switch($contact['type']) {
                case 'phone':
                  $href = 'tel:' . $contact['value'];
                  break;
                case 'email':
                  $href = 'mailto:' . $contact['value'];
                  break;
                case 'telegram':
                  $href = 'https://t.me/' . ltrim($contact['value'], '@');
                  $target = '_blank';
                  break;
                case 'whatsapp':
                  $href = 'https://wa.me/' . preg_replace('/[^0-9]/', '', $contact['value']);
                  $target = '_blank';
                  break;
                case 'website':
                case 'link':
                  $href = $contact['value'];
                  if (!str_starts_with($href, 'http')) {
                    $href = 'https://' . $href;
                  }
                  $target = '_blank';
                  break;
                default:
                  if (filter_var($contact['value'], FILTER_VALIDATE_URL)) {
                    $href = $contact['value'];
                    $target = '_blank';
                  }
              }
            ?>
            
            <a href="<?php echo e($href); ?>" class="contact-item" <?php if($target): ?> target="<?php echo e($target); ?>" <?php endif; ?>>
              <div class="contact-icon" style="background-color: <?php echo e($contact['color'] ?? '#007bff'); ?>;">
                <i class="fas fa-<?php echo e($contact['icon']); ?>"></i>
              </div>
              <div class="contact-info">
                <div class="contact-label"><?php echo e($contact['label']); ?></div>
                <div class="contact-value"><?php echo e($contact['value']); ?></div>
              </div>
            </a>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
      <?php endif; ?>
    </div>

    <!-- QR Code Section -->
    <div class="qr-section">
      <div class="qr-container">
        <canvas id="qrCodeCanvas" class="qr-code" width="120" height="120"></canvas>
      </div>
      <div class="qr-label">Scan to view business card</div>
    </div>

    <!-- Powered By -->
    <div class="powered-by">
      Powered by <a href="<?php echo e(url('/')); ?>">EVOLUTION888</a>
    </div>
  </div>

  <!-- QRious Library -->
  <script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js"></script>

  <script>
    // Add click tracking
    document.querySelectorAll('.contact-item').forEach(item => {
      item.addEventListener('click', function() {
        // You can add analytics tracking here
        console.log('Contact clicked:', this.querySelector('.contact-label').textContent);
      });
    });

    // Generate QR Code
    document.addEventListener('DOMContentLoaded', function() {
      const canvas = document.getElementById('qrCodeCanvas');
      const url = '<?php echo e($businessCard->public_url); ?>';

      // Determine size based on screen width
      const isMobile = window.innerWidth <= 480;
      const qrSize = isMobile ? 100 : 120;

      // Update canvas size
      canvas.width = qrSize;
      canvas.height = qrSize;

      try {
        // Создаем временный canvas для генерации QR кода
        const tempCanvas = document.createElement('canvas');
        const qrActualSize = qrSize - 8; // Оставляем место для центрирования

        const qr = new QRious({
          element: tempCanvas,
          value: url,
          size: qrActualSize,
          background: 'white',
          foreground: '#2d3748',
          level: 'H',
          padding: 0
        });

        // Очищаем основной canvas белым фоном
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, qrSize, qrSize);

        // Центрируем QR код на основном canvas
        const offsetX = (qrSize - qrActualSize) / 2;
        const offsetY = (qrSize - qrActualSize) / 2;
        ctx.drawImage(tempCanvas, offsetX, offsetY);

        // Add subtle animation after generation
        setTimeout(() => {
          canvas.style.opacity = '0';
          canvas.style.transform = 'scale(0.8)';
          setTimeout(() => {
            canvas.style.transition = 'all 0.3s ease';
            canvas.style.opacity = '1';
            canvas.style.transform = 'scale(1)';
          }, 100);
        }, 100);

      } catch (error) {
        console.error('QR Code generation error:', error);
        // Fallback: show text instead of QR
        const ctx = canvas.getContext('2d');
        const centerX = qrSize / 2;
        const centerY = qrSize / 2;

        ctx.fillStyle = '#666666';
        ctx.font = isMobile ? '10px Arial' : '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('📱', centerX, centerY - 15);
        ctx.fillText('QR Code', centerX, centerY);
        ctx.fillText('Not Available', centerX, centerY + 15);
      }

      // Handle long emails
      const emailElements = document.querySelectorAll('.contact-value');
      emailElements.forEach(element => {
        const text = element.textContent.trim();

        // Проверяем, является ли это email и длинный ли он
        if (text.includes('@') && text.length > 25) {
          element.classList.add('email-value', 'long-email');

          // Создаем сокращенную версию
          const atIndex = text.indexOf('@');
          const localPart = text.substring(0, atIndex);
          const domainPart = text.substring(atIndex);

          let shortVersion;
          if (localPart.length > 12) {
            shortVersion = localPart.substring(0, 8) + '...' + domainPart;
          } else {
            shortVersion = text.length > 30 ? text.substring(0, 20) + '...' : text;
          }

          // Создаем HTML для hover эффекта
          element.innerHTML = `
            <span class="email-short">${shortVersion}</span>
            <span class="email-full">${text}</span>
          `;
        } else if (text.includes('@')) {
          element.classList.add('email-value');
        }
      });

      // Add some interactive effects
      const card = document.querySelector('.business-card-container');

      // Add subtle hover effect to the entire card
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px)';
        this.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.15)';
      });

      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.1)';
      });
    });
  </script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/business-cards/show.blade.php ENDPATH**/ ?>