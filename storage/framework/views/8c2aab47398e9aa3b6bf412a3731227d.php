<?php $__env->startSection('title', 'Decline KYC - Admin Panel'); ?>

<?php $__env->startSection('admin-content'); ?>
    <div class="page-title-area">
        <div class="row align-items-center">
            <div class="col-sm-6">
                <div class="breadcrumbs-area clearfix">
                    <h4 class="page-title pull-left">Decline KYC</h4>
                    <ul class="breadcrumbs pull-left">
                        <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                        <li><a href="<?php echo e(route('admin.kyc.index')); ?>">All KYC</a></li>
                        <li><span>Decline</span></li>
                    </ul>
                </div>
            </div>
            <div class="col-sm-6 clearfix">
                <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
    <!-- Page Title Area End -->

    <div class="main-content-inner">
        <div class="row">
            <!-- Decline form -->
            <div class="col-12 mt-5">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title">Decline KYC Request</h4>
                        
                        <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                        <form action="<?php echo e(route('admin.kyc.update', $kyc->id)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>

                            <input type="hidden" name="action" value="decline" />

                            <div class="form-group">
                                <label for="reason">Reason for Decline</label>
                                <textarea name="reason" id="reason" class="form-control" rows="4" required></textarea>
                            </div>

                            <button type="submit" class="btn btn-danger">Decline</button>
                            <a href="<?php echo e(route('admin.kyc.index')); ?>" class="btn btn-secondary">Cancel</a>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/kyc/edit.blade.php ENDPATH**/ ?>