
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Payment Confirmation</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- Google Font -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #eef2f5;
            margin: 0;
            padding: 0;
        }
        .container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
            padding: 30px;
            text-align: center;
        }
        h1 {
            margin: 0 0 20px;
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }
        .info {
            font-size: 1rem;
            color: #555;
            margin: 15px 0;
        }
        .info strong {
            color: #000;
        }
        .btn-group {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 25px;
        }
        .btn {
            flex: 1;
            padding: 12px 0;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }
        .btn-primary {
            background-color: #28a745;
            color: #fff;
        }
        .btn-primary:hover {
            background-color: #218838;
        }
        .btn-secondary {
            background-color: #dc3545;
            color: #fff;
        }
        .btn-secondary:hover {
            background-color: #c82333;
        }
        @media (max-width: 480px) {
            .card { padding: 20px; }
            .btn { font-size: 0.9rem; }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="card">
        <h1>Confirm Your Payment</h1>
        <p class="info">
            You are about to send <strong><?php echo e($payload['amount']); ?> USDZ</strong> to partner
            <strong><?php echo e($partner->name); ?></strong>.
        </p>
        <p class="info">
            Your balance: <strong><?php echo e(auth()->user()->balance); ?> USDZ</strong>
        </p>
        <form method="POST" action="<?php echo e(route('partner.pay.confirm')); ?>" id="payForm">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="token" value="<?php echo e($token); ?>">
            <div class="btn-group">
                <button type="submit" id="payBtn" class="btn btn-primary">Pay</button>
                <button type="button" class="btn btn-secondary cancel" onclick="window.location='<?php echo e(url()->previous()); ?>'">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const userBalance = parseFloat(<?php echo e(auth()->user()->balance); ?>);
    const amountToPay = parseFloat(<?php echo e($payload['amount']); ?>);
    const payBtn = document.getElementById('payBtn');
    const form = document.getElementById('payForm');

    if (userBalance < amountToPay) {
        Swal.fire({
            icon: 'error',
            title: 'Insufficient USDZ',
            text: 'You do not have enough USDZ to complete this payment.',
        });
        payBtn.disabled = true;
    }

    form.addEventListener('submit', e => {
        if (userBalance < amountToPay) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Payment cannot be processed due to insufficient USDZ.',
            });
        }
    });
});
</script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/partner/pay_confirm.blade.php ENDPATH**/ ?>