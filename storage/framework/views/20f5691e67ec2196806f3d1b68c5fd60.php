<?php $__env->startSection('title', 'KY<PERSON> Details - Admin Panel'); ?>

<?php $__env->startSection('admin-content'); ?>
    <div class="page-title-area">
        <div class="row align-items-center">
            <div class="col-sm-6">
                <div class="breadcrumbs-area clearfix">
                    <h4 class="page-title pull-left">KYC Details</h4>
                    <ul class="breadcrumbs pull-left">
                        <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                        <li><a href="<?php echo e(route('admin.kyc.index')); ?>">All KYC</a></li>
                        <li><span>Show</span></li>
                    </ul>
                </div>
            </div>
            <div class="col-sm-6 clearfix">
                <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
    <!-- Page Title Area End -->

    <div class="main-content-inner">
        <div class="row">
            <!-- KYC show -->
            <div class="col-12 mt-5">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title">KYC Request #<?php echo e($kyc->id); ?></h4>
                        
                        <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                        <p><strong>User:</strong> <?php echo e(optional($kyc->user)->email); ?></p>
                        <p><strong>Full Name:</strong> <?php echo e($kyc->full_name); ?></p>
                        <p><strong>Status:</strong>
                            <?php if($kyc->approved): ?>
                                <span class="badge badge-success">Approved</span>
                            <?php else: ?>
                                <span class="badge badge-warning">Pending</span>
                            <?php endif; ?>
                        </p>

                        <?php if($kyc->documents): ?>
                            <hr>
                            <h5>Documents:</h5>
                            <ul>
                                <?php $__currentLoopData = $kyc->documents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $doc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li>
                                        <?php echo e($doc['name'] ?? 'Document'); ?> (<?php echo e($doc['mime'] ?? 'unknown type'); ?>)
                                        <!-- Если это Base64 изображение, можно вывести превью -->
                                        <?php if(str_starts_with($doc['mime'] ?? '', 'image/') && !empty($doc['data'])): ?>
                                            <div>
                                                <img src="data:<?php echo e($doc['mime']); ?>;base64,<?php echo e($doc['data']); ?>" 
                                                     alt="Document"
                                                     style="max-width: 200px; border:1px solid #ccc; margin-top:5px;">
                                            </div>
                                        <?php endif; ?>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        <?php endif; ?>

                        <a href="<?php echo e(route('admin.kyc.index')); ?>" class="btn btn-secondary mt-3">Back</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/kyc/show.blade.php ENDPATH**/ ?>