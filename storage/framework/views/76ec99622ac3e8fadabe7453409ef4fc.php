<?php $__env->startSection('title'); ?>
Project Create - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.18/summernote-lite.min.css" rel="stylesheet">
<style>
    .form-check-label {
        text-transform: capitalize;
    }
    .image-preview {
        width: 100px;
        height: 100px;
        margin-top: 10px;
        border: 1px solid #dddddd;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #cccccc;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>
<!-- Page Title Area Start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Create Project</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><a href="<?php echo e(route('admin.projects.index')); ?>">All Projects</a></li>
                    <li><span>Create Project</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- Page Title Area End -->

<div class="main-content-inner">
    <div class="row">
        <!-- Form Start -->
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Create New Project</h4>
                    <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    
                    <form action="<?php echo e(route('admin.projects.store')); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="name">Name (Max 64 Characters)</label>
                                <input type="text" class="form-control" id="name" name="name" placeholder="Enter Name" required>
                            </div>
                        </div>
                        <!-- Новое поле category -->
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="category">Category (Max 64 Characters)</label>
                                <input type="text" class="form-control" id="category" name="category" placeholder="Enter Category" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-12">
                                <label for="description">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="4" placeholder="Enter Full Description" required></textarea>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-12">
                                <label for="img">Upload Image</label>
                                <input type="file" class="form-control" id="img" name="img" accept="image/*" onchange="previewImage();">
                                <div class="image-preview" id="imagePreview">
                                    <span>Image Preview</span>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary mt-4 pr-4 pl-4">Create Project</button>
                    </form>
                </div>
            </div>
        </div>
        <!-- Form End -->
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.18/summernote-lite.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $('#description').summernote({
            height: 300,
            minHeight: null,
            maxHeight: null,
            focus: true
        });
        $('.select2').select2();
    });

    function previewImage() {
        const file = document.querySelector('#img').files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const imgElement = document.createElement('img');
                imgElement.src = e.target.result;
                imgElement.style.width = '100px';
                imgElement.style.height = '100px';
                document.querySelector('#imagePreview').innerHTML = '';
                document.querySelector('#imagePreview').appendChild(imgElement);
            };
            reader.readAsDataURL(file);
        }
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/projects/create.blade.php ENDPATH**/ ?>