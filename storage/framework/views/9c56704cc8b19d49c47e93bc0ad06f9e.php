<?php $__env->startSection('title'); ?>
Create Role - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .form-check-label {
        text-transform: capitalize;
    }
    .permission-group {
        margin-bottom: 1rem;
    }
    .permission-group-header {
        font-weight: bold;
    }
    .permission-subgroup-header {
        margin-left: 2rem;
        font-weight: 500;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>

<!-- Page Title Area Start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Create Role</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><a href="<?php echo e(route('admin.role.index')); ?>">All Roles</a></li>
                    <li><span>Create Role</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- Page Title Area End -->

<div class="main-content-inner">
    <div class="row">
        <!-- Form Start -->
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Create New Role</h4>
                    <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <form action="<?php echo e(route('admin.role.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="form-group">
                            <label for="name">Role Name</label>
                            <input type="text" class="form-control" id="name" name="name" placeholder="Enter Role Name" required>
                        </div>

                        <div class="form-group">
                            <label for="permissions">Permissions</label>
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="checkPermissionAll" value="1" onclick="toggleAllPermissions(this)">
                                <label class="form-check-label" for="checkPermissionAll">All</label>
                            </div>

                            <hr>

                            <!-- Displaying permission groups vertically -->
                            <?php $__currentLoopData = $permissionGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group => $subGroups): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="permission-group">
                                    <div class="form-check permission-group-header">
                                        <label class="form-check-label" for="checkPermissionGroup<?php echo e($group); ?>"><?php echo e(ucfirst($group)); ?></label>
                                    </div>
                                    
                                    <div class="ml-4">
                                        <?php $__currentLoopData = $subGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subGroup => $permissions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="permission-subgroup-header">
                                                <input type="checkbox" class="form-check-input" id="checkSubGroup<?php echo e($subGroup); ?>" onclick="toggleSubGroupPermissions('<?php echo e($group); ?>', '<?php echo e($subGroup); ?>', this)">
                                                <label class="form-check-label" for="checkSubGroup<?php echo e($subGroup); ?>"><?php echo e(ucfirst($subGroup)); ?></label>
                                            </div>
                                            <div class="ml-5">
                                                <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="form-check">
                                                        <input type="checkbox" class="form-check-input permission-group-<?php echo e($group); ?> permission-subgroup-<?php echo e($subGroup); ?>" name="permissions[]" id="checkPermission<?php echo e($permission->id); ?>" value="<?php echo e($permission->name); ?>">
                                                        <label class="form-check-label" for="checkPermission<?php echo e($permission->id); ?>"><?php echo e($permission->name); ?></label>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                                <hr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <button type="submit" class="btn btn-primary mt-4">Save Role</button>
                    </form>
                </div>
            </div>
        </div>
        <!-- Form End -->
        
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    function toggleAllPermissions(source) {
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = source.checked;
        });
    }
    function toggleSubGroupPermissions(groupName, subGroupName, source) {
        const subGroupCheckboxes = document.querySelectorAll('.permission-group-' + groupName + '.permission-subgroup-' + subGroupName);
        subGroupCheckboxes.forEach(checkbox => {
            checkbox.checked = source.checked;
        });
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/role/create.blade.php ENDPATH**/ ?>