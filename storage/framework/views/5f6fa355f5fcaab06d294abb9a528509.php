<!DOCTYPE html>
<html data-wf-page="6759b8b8b8b8b8b8b8b8b8b8" data-wf-site="6759b8b8b8b8b8b8b8b8b8b8">
<head>
    <meta charset="utf-8">
    <title>EVOLUTION888 business center - Create Business Card</title>
    <meta content="Create Business Card" property="og:title">
    <meta content="Create Business Card" property="twitter:title">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <meta content="Webflow" name="generator">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <link href="/css/techbetatemplates.webflow.14600f26e.css" rel="stylesheet" type="text/css">
    <script src="https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js" type="text/javascript"></script>
    <script type="text/javascript">WebFont.load({google: {families: ["Montserrat:100,100italic,200,200italic,300,300italic,400,400italic,500,500italic,600,600italic,700,700italic,800,800italic,900,900italic"]}});</script>
    <script type="text/javascript">!function(o,c){var n=c.documentElement,t=" w-mod-";n.className+=t+"js",("ontouchstart"in o||o.DocumentTouch&&c instanceof DocumentTouch)&&(n.className+=t+"touch")}(window,document);</script>
    <link href="/images/favicon.ico" rel="shortcut icon" type="image/x-icon">
    <link href="/images/webclip.png" rel="apple-touch-icon">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr !important;
            }
            .form-container {
                padding: 20px !important;
                margin: 10px !important;
            }
        }
    </style>
</head>
<body class="body">
    <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
    <div class="section" style="padding: 40px 0;">
        <div class="w-layout-blockcontainer container-default w-container">
            <div class="form-container" style="max-width: 800px; margin: 0 auto; background: #fff; border-radius: 12px; padding: 40px; box-shadow: 0 8px 30px rgba(0,0,0,0.1); border: 1px solid #e0e0e0;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h2 style="font-size: 28px; margin-bottom: 10px; color: #333;">Create Business Card</h2>
                    <p style="color: #666; margin-bottom: 20px;">Fill out the form below to create your professional business card</p>
                    <a href="<?php echo e(route('dashboard.business-cards.index')); ?>" class="primary-button dark-mode w-inline-block" style="margin-bottom: 20px;">
                        <div class="text-block">← Back to Cards</div>
                    </a>
                </div>

                <form id="businessCardForm" action="<?php echo e(route('dashboard.business-cards.store')); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>

                    <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="title" style="display: block; font-weight: 600; margin-bottom: 8px; color: #333;">Card Title *</label>
                            <input type="text" id="title" name="title" class="input w-input" placeholder="Enter card title" required style="width: 100%; margin-bottom: 0;">
                        </div>

                        <div>
                            <label for="profile_name" style="display: block; font-weight: 600; margin-bottom: 8px; color: #333;">Profile Name (URL) *</label>
                            <input type="text" id="profile_name" name="profile_name" class="input w-input" placeholder="your-profile-name" required style="width: 100%; margin-bottom: 0;">
                            <div style="font-size: 12px; color: #666; margin-top: 4px;">This will be your card URL: /tag/your-profile-name</div>
                        </div>
                    </div>

                    <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="company" style="display: block; font-weight: 600; margin-bottom: 8px; color: #333;">Company</label>
                            <input type="text" id="company" name="company" class="input w-input" placeholder="Your Company Name" style="width: 100%; margin-bottom: 0;">
                        </div>

                        <div>
                            <label for="position" style="display: block; font-weight: 600; margin-bottom: 8px; color: #333;">Position</label>
                            <input type="text" id="position" name="position" class="input w-input" placeholder="Your Position" style="width: 100%; margin-bottom: 0;">
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="description" style="display: block; font-weight: 600; margin-bottom: 8px; color: #333;">Description</label>
                        <textarea id="description" name="description" class="input w-input" placeholder="Brief description about yourself or your business" style="width: 100%; height: 100px; resize: vertical; margin-bottom: 0;"></textarea>
                    </div>

                    <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="email" style="display: block; font-weight: 600; margin-bottom: 8px; color: #333;">Email</label>
                            <input type="email" id="email" name="email" class="input w-input" placeholder="<EMAIL>" style="width: 100%; margin-bottom: 0;">
                        </div>

                        <div>
                            <label for="phone" style="display: block; font-weight: 600; margin-bottom: 8px; color: #333;">Phone</label>
                            <input type="tel" id="phone" name="phone" class="input w-input" placeholder="+1234567890" style="width: 100%; margin-bottom: 0;">
                        </div>
                    </div>

                    <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="website" style="display: block; font-weight: 600; margin-bottom: 8px; color: #333;">Website</label>
                            <input type="url" id="website" name="website" class="input w-input" placeholder="https://yourwebsite.com" style="width: 100%; margin-bottom: 0;">
                        </div>

                        <div>
                            <label for="location" style="display: block; font-weight: 600; margin-bottom: 8px; color: #333;">Location</label>
                            <input type="text" id="location" name="location" class="input w-input" placeholder="City, Country" style="width: 100%; margin-bottom: 0;">
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="logo" style="display: block; font-weight: 600; margin-bottom: 8px; color: #333;">Logo</label>
                        <input type="file" id="logo" name="logo" class="input w-input" accept="image/*" style="width: 100%; margin-bottom: 0;">
                        <div style="font-size: 12px; color: #666; margin-top: 4px;">Upload your company logo or profile picture</div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="social_links" style="display: block; font-weight: 600; margin-bottom: 8px; color: #333;">Social Media Links</label>
                        <textarea id="social_links" name="social_links" class="input w-input" placeholder="Enter social media links (one per line)&#10;https://twitter.com/yourhandle&#10;https://linkedin.com/in/yourprofile" style="width: 100%; height: 80px; resize: vertical; margin-bottom: 0;"></textarea>
                    </div>

                    <div style="margin-bottom: 30px;">
                        <label for="tags" style="display: block; font-weight: 600; margin-bottom: 8px; color: #333;">Tags</label>
                        <input type="text" id="tags" name="tags" class="input w-input" placeholder="business, technology, consulting (comma separated)" style="width: 100%; margin-bottom: 0;">
                        <div style="font-size: 12px; color: #666; margin-top: 4px;">Add relevant tags to help people find your card</div>
                    </div>

                    <div style="text-align: center; display: flex; gap: 15px; justify-content: center;">
                        <button type="submit" class="primary-button dark-mode w-inline-block">
                            <div class="text-block">Create Business Card</div>
                        </button>
                        <a href="<?php echo e(route('dashboard.business-cards.index')); ?>" class="primary-button w-inline-block" style="background-color: #6c757d;">
                            <div class="text-block">Cancel</div>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
    <script src="https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=6759b8b8b8b8b8b8b8b8b8b8" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
    <script src="/js/webflow.3de03aa26.js" type="text/javascript"></script>
    
    <script>
        document.getElementById('businessCardForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'Business card created successfully!',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        window.location.href = '<?php echo e(route("dashboard.business-cards.index")); ?>';
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: data.message || 'Failed to create business card',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    title: 'Error!',
                    text: 'An error occurred while creating the business card',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            });
        });
        
        // Profile name validation
        document.getElementById('profile_name').addEventListener('input', function() {
            this.value = this.value.toLowerCase().replace(/[^a-z0-9-]/g, '');
        });
    </script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/dashboard/business-cards/create.blade.php ENDPATH**/ ?>