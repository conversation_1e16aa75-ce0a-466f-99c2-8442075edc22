<?php $__env->startSection('title', 'Withdrawal Request Details - Admin Panel'); ?>

<?php $__env->startSection('admin-content'); ?>
    <div class="page-title-area">
        <div class="row align-items-center">
            <div class="col-sm-6">
                <div class="breadcrumbs-area clearfix">
                    <h4 class="page-title pull-left">Withdrawal Request Details</h4>
                    <ul class="breadcrumbs pull-left">
                        <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                        <li><a href="<?php echo e(route('admin.withdrawals.index')); ?>">All Withdrawals</a></li>
                        <li><span>Show</span></li>
                    </ul>
                </div>
            </div>
            <div class="col-sm-6 clearfix">
                <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
    <!-- Page Title Area End -->

    <div class="main-content-inner">
        <div class="row">
            <!-- Withdrawal Request Show -->
            <div class="col-12 mt-5">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title">Withdrawal Request #<?php echo e($withdrawal->id); ?></h4>
                        
                        <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                        
                        <p><strong>User:</strong> 
                            <?php echo e(optional($withdrawal->user)->email ?? 'Unknown User'); ?>

                        </p>

                        
                        <p><strong>Payment Method:</strong> 
                            <?php if($withdrawal->paymentMethodRel): ?>
                                <img src="<?php echo e($withdrawal->paymentMethodRel->img); ?>"
                                     alt="<?php echo e($withdrawal->paymentMethodRel->name); ?>"
                                     style="height: 20px; margin-right:5px; vertical-align: middle;">
                                <?php echo e($withdrawal->paymentMethodRel->name); ?>

                            <?php else: ?>
                                <em>Unknown method (<?php echo e($withdrawal->payment_method); ?>)</em>
                            <?php endif; ?>
                        </p>

                        <p><strong>Address:</strong> <?php echo e($withdrawal->address); ?></p>
                        <p><strong>Amount:</strong> <?php echo e($withdrawal->amount); ?></p>
                        <p><strong>Status:</strong>
                            <?php if($withdrawal->status === 'approved'): ?>
                                <span class="badge badge-success">Approved</span>
                            <?php elseif($withdrawal->status === 'rejected'): ?>
                                <span class="badge badge-danger">Rejected</span>
                            <?php else: ?>
                                <span class="badge badge-warning">Pending</span>
                            <?php endif; ?>
                        </p>
                        <p><strong>Requested At:</strong> 
                            <?php echo e($withdrawal->created_at->format('Y-m-d H:i:s')); ?>

                        </p>

                        <a href="<?php echo e(route('admin.withdrawals.index')); ?>" class="btn btn-secondary mt-3">Back</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/withdrawals/show.blade.php ENDPATH**/ ?>