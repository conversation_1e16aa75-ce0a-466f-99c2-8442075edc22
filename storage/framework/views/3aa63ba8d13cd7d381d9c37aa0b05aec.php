<?php $__env->startSection('title'); ?>
Pending Business Cards - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
    <style>
        .card-preview {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            background: #f8f9fa;
        }
        .card-logo {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
        }
        .contact-item {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            background: #e9ecef;
            border-radius: 4px;
            font-size: 12px;
        }
        .moderation-actions {
            margin-top: 15px;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>

<!-- Page Title Area Start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Pending Business Cards</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><a href="<?php echo e(route('admin.business-cards.index')); ?>">Business Cards</a></li>
                    <li><span>Pending</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- Page Title Area End -->

<div class="main-content-inner">
    <div class="row">
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title float-left">Pending Business Cards (<?php echo e($cards->total()); ?>)</h4>
                    <p class="float-right mb-2">
                        <a class="btn btn-secondary text-white" href="<?php echo e(route('admin.business-cards.index')); ?>">
                            <i class="fa fa-arrow-left"></i> Back to All Cards
                        </a>
                    </p>
                    <div class="clearfix"></div>
                    
                    <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    
                    <?php if($cards->count() > 0): ?>
                        <?php $__currentLoopData = $cards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $card): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="card-preview">
                            <div class="row">
                                <div class="col-md-2">
                                    <?php if($card->logo): ?>
                                        <img src="<?php echo e(Storage::url($card->logo)); ?>" alt="Logo" class="card-logo">
                                    <?php else: ?>
                                        <div class="card-logo bg-light d-flex align-items-center justify-content-center">
                                            <i class="fa fa-image text-muted fa-2x"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="mb-2"><?php echo e($card->title); ?></h5>
                                    <p class="text-muted mb-2">
                                        <strong>Profile:</strong> 
                                        <a href="<?php echo e($card->public_url); ?>" target="_blank" class="text-primary">
                                            <?php echo e($card->profile_name); ?>

                                        </a>
                                    </p>
                                    <?php if($card->description): ?>
                                        <p class="mb-2"><?php echo e($card->description); ?></p>
                                    <?php endif; ?>
                                    
                                    <?php if($card->contacts && count($card->contacts) > 0): ?>
                                        <div class="mb-2">
                                            <strong>Contacts:</strong><br>
                                            <?php $__currentLoopData = $card->formatted_contacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="contact-item">
                                                    <i class="fa fa-<?php echo e($contact['icon']); ?>"></i>
                                                    <?php echo e($contact['label'] ?: $contact['value']); ?>

                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-right">
                                        <p class="mb-1">
                                            <strong>User:</strong> <?php echo e($card->user->name ?? 'N/A'); ?><br>
                                            <small class="text-muted"><?php echo e($card->user->email ?? 'N/A'); ?></small>
                                        </p>
                                        <p class="mb-1">
                                            <strong>Created:</strong> <?php echo e($card->created_at->format('M d, Y H:i')); ?>

                                        </p>
                                        <p class="mb-1">
                                            <strong>Views:</strong> <?php echo e(number_format($card->views_count)); ?>

                                        </p>
                                        
                                        <div class="moderation-actions">
                                            <button class="btn btn-success btn-sm approve-btn" data-id="<?php echo e($card->_id); ?>">
                                                <i class="fa fa-check"></i> Approve
                                            </button>
                                            <button class="btn btn-danger btn-sm reject-btn" data-id="<?php echo e($card->_id); ?>">
                                                <i class="fa fa-times"></i> Reject
                                            </button>
                                            <a class="btn btn-info btn-sm" href="<?php echo e(route('admin.business-cards.show', $card->_id)); ?>">
                                                <i class="fa fa-eye"></i> View
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        
                        <!-- Pagination -->
                        <div class="mt-3">
                            <?php echo e($cards->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fa fa-check-circle fa-3x text-success mb-3"></i>
                            <h4>No Pending Business Cards</h4>
                            <p class="text-muted">All business cards have been moderated.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Moderation Modal -->
<div class="modal fade" id="moderationModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Moderate Business Card</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="moderationForm">
                    <input type="hidden" id="cardId">
                    <input type="hidden" id="action">
                    <div class="form-group">
                        <label>Notes</label>
                        <textarea class="form-control" id="notes" rows="3" placeholder="Add moderation notes..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmModeration">Confirm</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Approve button
            $('.approve-btn').click(function() {
                const cardId = $(this).data('id');
                $('#cardId').val(cardId);
                $('#action').val('approve');
                $('.modal-title').text('Approve Business Card');
                $('#notes').attr('placeholder', 'Add approval notes (optional)...');
                $('#moderationModal').modal('show');
            });

            // Reject button
            $('.reject-btn').click(function() {
                const cardId = $(this).data('id');
                $('#cardId').val(cardId);
                $('#action').val('reject');
                $('.modal-title').text('Reject Business Card');
                $('#notes').attr('placeholder', 'Add rejection reason (required)...');
                $('#moderationModal').modal('show');
            });

            // Confirm moderation
            $('#confirmModeration').click(function() {
                const cardId = $('#cardId').val();
                const action = $('#action').val();
                const notes = $('#notes').val();
                
                if (action === 'reject' && !notes.trim()) {
                    alert('Rejection reason is required');
                    return;
                }
                
                $.post(`/admin/business-cards/${cardId}/${action}`, {
                    _token: '<?php echo e(csrf_token()); ?>',
                    notes: notes
                }).done(function(response) {
                    if (response.success) {
                        location.reload();
                    }
                }).fail(function() {
                    alert('Error occurred. Please try again.');
                });
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/business-cards/pending.blade.php ENDPATH**/ ?>