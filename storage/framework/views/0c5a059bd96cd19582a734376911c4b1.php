<!DOCTYPE html>
<!-- This site was created in Zer0lab. https://dev1.it.com -->
<!-- Last Published: Wed Feb 15 2025 00:00:00 GMT+0000 (Coordinated Universal Time) -->
<html data-wf-domain="techbetatemplates.webflow.io" data-wf-page="663b748d327c826a2952af5b" data-wf-site="663b748d327c826a2952af46" data-wf-status="1" lang="en" data-wf-collection="663b748d327c826a2952afbd" data-wf-item-slug="basic" class=" w-mod-js w-mod-ix">

<head>
    <style>
        .wf-force-outline-none[tabindex="-1"]:focus {
          outline: none;
        }
    </style>
    <meta charset="utf-8">
    <title>Store of Digital Certificates, Vouchers & PIN Codes with unique cashback privileges for EVOLUTION888 members.</title>
  <meta content="IMI Club - много функциональная социальная инновационная эко-система сервисов, приложений и продуктов для человека по всем сферам жизни, который включает в себя IMI Store - гибридный партнерский CPA маркет товаров и услуг с кешбэк привилегиями." name="description">
  <meta content="IMI Shop Club Cash Back systems " property="og:title">
  <meta content="IMI Club - много функциональная социальная инновационная эко-система сервисов, приложений и продуктов для человека по всем сферам жизни, который включает в себя IMI Store - гибридный партнерский CPA маркет товаров и услуг с кешбэк привилегиями." property="og:description">
  <meta content="https://cdn.prod.website-files.com/663b748d327c826a2952af46/66f7158e303df70c2274a356_techbeta-x-technology-app-webflow-template.png" property="og:image">
  <meta content="Магазин цифровых сертификатов, ваучеров и пин-кодов с уникальными кешбэк-привилегиями" property="twitter:title">
  <meta content="IMI Club - много функциональная социальная инновационная эко-система сервисов, приложений и продуктов для человека по всем сферам жизни, который включает в себя IMI Store - гибридный партнерский CPA маркет товаров и услуг с кешбэк привилегиями." property="twitter:description">
  <meta content="https://cdn.prod.website-files.com/663b748d327c826a2952af46/66f7158e303df70c2274a356_techbeta-x-technology-app-webflow-template.png" property="twitter:image">
  <meta property="og:type" content="website">
  <meta content="summary_large_image" name="twitter:card">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <meta content="IMI" name="generator">
    <!-- Если CSRF-токен используется, убедитесь, что он присутствует -->
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <link href="css/techbetatemplates.webflow.14600f26e.css" rel="stylesheet" type="text/css">
    <script type="text/javascript">
        ! function(o, c) {
          var n = c.documentElement,
            t = " w-mod-";
          n.className += t + "js", ("ontouchstart" in o || o.DocumentTouch && c instanceof DocumentTouch) && (n.className += t + "touch")
        }(window, document);
    </script>
    <link href="https://cdn.prod.website-files.com/663b748d327c826a2952af46/663b95dbf2fa1adf0f4e484b_favicon-techbeta-webflow-template.svg" rel="shortcut icon" type="image/x-icon">
    <link href="https://cdn.prod.website-files.com/663b748d327c826a2952af46/663b95dd7fa9fba75b9cfcdc_webclip-techbeta-webflow-template.svg" rel="apple-touch-icon">
    <style>
        .w-webflow-badge {
          display: none !important;
        }
    </style>
</head>

<body>
    <div class="page-wrapper">
        <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <section class="section top">
            <div class="w-layout-blockcontainer container-default w-container">
                <div data-w-id="b136c17c-4ed3-6f0b-1597-53373fda4e32" style="transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg); opacity: 1; transform-style: preserve-3d;" class="card plan-page---main">
                    <div class="card plan-page-content">
                        <div>
                            <div class="heading-wrapper">
                                <h2 class="display-6 title-tag text-break-all---mbp"><?php echo e($data->title); ?></h2>
                            </div>
                            <div class="mg-top-small mg-top-16px---mbl">
                                <div class="inner-container _375px _100-mbl">
                                    <div class="inner-container _550px---mbl">
                                        <p><?php echo $data->description; ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="w-node-eeccf282-ea05-7707-b972-c5c1cbe2f364-2952af5b" class="add-to-cart---main-content">
                            <div class="add-to-cart---content-top">
                                <h2 class="display-4 mid"><?php echo e($data->short_description); ?></h2>
                                <div class="mg-top-extra-small">
                                    <div class="inner-container _340px">
                                    </div>
                                </div>
                            </div>
                            <div class="add-to-cart---content-bottom">
                                <div class="display-price-wrapper plan-page---price">
                                    <div class="display-8 mid text-titles"><?php echo e($data->price); ?></div>
                                    <div class="display-price---right">
                                        <div class="display-3 mid">/ USDZ Tokens</div>
                                    </div>
                                </div>
                                <div class="mg-top-small mg-top-16px---mbl">
                                    <div class="add-to-cart">
                                        <input onclick="buy_product('<?php echo e($data->_id); ?>')" type="submit" data-node-type="commerce-add-to-cart-button" data-loading-text="Loading..." aria-busy="false" aria-haspopup="dialog" class="w-commerce-commerceaddtocartbutton primary-button" value="Buy now">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
		<!--
        <section class="section tiny bottom-v2">
            <div class="w-layout-blockcontainer container-default w-container">
                <div class="w-layout-grid grid-2-columns product-rich-text-grid">
                    <div data-w-id="e1ce05e3-3832-d1eb-563f-1f7d3fb0dc46" style="opacity: 1;" class="inner-container _690px _100-mbl">
                        <div class="mg-bottom--16px">
                            <div class="rich-text w-richtext">
                                <h2>What’s included?</h2>
                                <p>Venenatis sollicitudin posuere elit consequat et enim. Neque tortor amet dictum tempor. Leo facilisis aliquet viverra scelerisque eleifend viverra est. At massa erat vel amet enim laoreet dictum pellentesque. Urna cursus quam pulvinar tellus. Duis fermentum nibh volutpat morbi. Et ac sed ultricies ut nunc sodales lectus. Ultricies pharetra mauris eget pellentesque accumsan</p>
                                <ul role="list">
                                    <li>Morbi fringilla molestie magna sed dictum. Praesent pharetra turpis augue.</li>
                                    <li>Cras mi purus, viverra vitae felis sit amet, tincidunt fringilla lorem.</li>
                                    <li>Non mattis urna ex nec sem. Donec varius diam et suscipit venenati</li>
                                    <li>Non mattis urna ex nec sem. Donec varius diam et suscipit venenati</li>
                                    <li>Non mattis urna ex nec sem. Donec varius diam et suscipit venenati</li>
                                </ul>
                                <p>Et urna ac et maecenas fusce amet. Nibh nec commodo massa sed. Tincidunt porttitor in pharetra egestas sit neque ac lacus. Amet a nunc et cum. Odio at volutpat volutpat in leo eget ipsum diam elementum.</p>
                                <p>Venenatis sollicitudin posuere elit consequat et enim. Aliquet viverra scelerisque eleifend viverra est. <a href="/home-pages/home-v1">At massa erat vel amet enim</a></p>
                            </div>
                        </div>
                    </div>
                    <div id="w-node-_6e289a08-50be-ad70-03d8-fcf8582ace2a-2952af5b" class="sticky-top">
                        <div id="w-node-f8d54cf7-d7db-7431-9baa-378fecf7a765-2952af5b" data-w-id="f8d54cf7-d7db-7431-9baa-378fecf7a765" style="transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg); opacity: 1; transform-style: preserve-3d;" class="card custom-plan-card">
                            <div class="icon-wrapper"><img src="images/66465cdde89cde47fcab5207_message-icon-techbeta-x-webflow-template.svg" loading="eager" alt="Message Icon - Techbeta X Webflow Template"></div>
                            <div class="inner-container _500px---mbl">
                                <div class="mg-top-small mg-top-16px---mbl">
                                    <div class="text-light">
                                        <div class="display-6 mid">Need a custom plan?<br>‍<span class="text-no-wrap">Contact us</span></div>
                                    </div>
                                </div>
                                <div class="mg-top-extra-small">
                                    <div class="text-neutral-light">
                                        <p>Lorem ipsum dolor sit amet consectetur tellus elementum turpis vitae pretium id enim scelerisque lobortis <span class="text-no-wrap">vitae vel elit.</span></p>
                                    </div>
                                </div>
                            </div>
                            <div class="mg-top-medium mg-top-32px---mbl">
                                <div class="buttons-row left"><a id="w-node-_6024598d-eaa2-3e85-ac05-fde8b7e66609-b7e66609" href="/company-pages/contact" class="primary-button dark-mode w-inline-block">
                                        <div class="text-block">Get in touch</div>
                                    </a></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
		-->
        <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </div>
 
    <script src="js/jquery-3.5.1.min.dc5e7f18c8.js" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
    <script src="js/webflow.3de03aa26.js" type="text/javascript"></script>

    <script>
        function buy_product(productId) {
            // Показываем подтверждение покупки с помощью SweetAlert2
            Swal.fire({
                icon: 'question',
                title: 'Are you sure about this action?',
                showCancelButton: true,
                confirmButtonText: 'Yes, I confirm!',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                    
                    fetch('/api/product/buy', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken
                        },
                        body: JSON.stringify({ productId: productId })
                    })
                    .then(function(res) {
                        return res.json();
                    })
                    .then(function(data) {
                        if (data.status === 'success') {
                            Swal.fire({
                                icon: 'success',
                                title: 'Purchase successful!',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: data.message || 'Purchase failed',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(function() {
                        Swal.fire({
                            icon: 'error',
                            title: 'Request Error',
                            confirmButtonText: 'OK'
                        });
                    });
                }
            });
        }
    </script>

</body>

</html>
<?php /**PATH /var/www/club/resources/views/dashboard/shop_product_view.blade.php ENDPATH**/ ?>