<?php $__env->startSection('title'); ?>
Create Partner - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
<style>
    .form-check-label {
        text-transform: capitalize;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>

<!-- page title area start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Create Partner</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><a href="<?php echo e(route('admin.partners.index')); ?>">All Partners</a></li>
                    <li><span>Create Partner</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- page title area end -->

<div class="main-content-inner">
    <div class="row">
        <!-- form start -->
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Create New Partner</h4>
                    <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    
                    <form action="<?php echo e(route('admin.partners.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        
                        <!-- Name (название партнёра) -->
                        <div class="form-row">
                            <div class="form-group col-md-8">
                                <label for="name">Partner Name</label>
                                <input type="text" class="form-control" id="name" name="name"
                                       placeholder="Enter Partner Name" required>
                            </div>
                        </div>

                        <!-- Redirect URL -->
                        <div class="form-row">
                            <div class="form-group col-md-8">
                                <label for="redirect_url">Redirect URL</label>
                                <input type="url" class="form-control" id="redirect_url" name="redirect_url"
                                       placeholder="https://example.com/callback" required>
                            </div>
                        </div>
						<div class="form-row">
							<div class="form-group col-md-8">
								<label for="callback_url">Callback URL</label>
								<input type="url" class="form-control" id="callback_url" name="callback_url"
									   placeholder="https://example.com/payment/callback">
							</div>
						</div>
                        <button type="submit" class="btn btn-primary mt-4 pr-4 pl-4">Create Partner</button>
                    </form>

                </div>
            </div>
        </div>
        <!-- form end -->
        
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2();
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/partners/create.blade.php ENDPATH**/ ?>