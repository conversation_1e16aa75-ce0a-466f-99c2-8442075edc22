<!DOCTYPE html>
<html data-wf-domain="/" data-wf-page="663d095d8e07618c5381377b" data-wf-site="663b748d327c826a2952af46" data-wf-status="1" lang="en" class=" w-mod-js w-mod-ix">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Subscription Required - EVOLUTION888</title>

  <!-- CSRF Token (Laravel) -->
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
  <!-- Main CSS -->
  <link rel="stylesheet" href="<?php echo e(asset('css/style.css')); ?>" />
  <link href="/css/techbetatemplates.webflow.14600f26e.css?v=0.3" rel="stylesheet" type="text/css">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <style>
    .subscription-required-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 20px;
    }

    .subscription-card {
      background: white;
      border-radius: 24px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
      max-width: 600px;
      width: 100%;
      overflow: hidden;
      text-align: center;
    }

    .subscription-header {
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
      padding: 60px 40px;
      color: white;
      position: relative;
    }

    .subscription-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
    }

    .lock-icon {
      font-size: 4rem;
      margin-bottom: 20px;
      position: relative;
      z-index: 2;
    }

    .subscription-header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 15px;
      position: relative;
      z-index: 2;
    }

    .subscription-header p {
      font-size: 1.2rem;
      opacity: 0.9;
      position: relative;
      z-index: 2;
    }

    .subscription-body {
      padding: 50px 40px;
    }

    .feature-preview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 30px;
      margin: 40px 0;
    }

    .feature-item {
      text-align: center;
      padding: 30px 20px;
      background: #f8f9fa;
      border-radius: 16px;
      transition: transform 0.3s ease;
    }

    .feature-item:hover {
      transform: translateY(-5px);
    }

    .feature-icon {
      font-size: 2.5rem;
      color: #667eea;
      margin-bottom: 20px;
    }

    .feature-title {
      font-size: 1.3rem;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 10px;
    }

    .feature-description {
      color: #7f8c8d;
      font-size: 0.95rem;
      line-height: 1.5;
    }

    .cta-section {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      margin: 40px -40px -50px;
      padding: 40px;
      color: white;
    }

    .cta-title {
      font-size: 1.8rem;
      font-weight: 700;
      margin-bottom: 15px;
    }

    .cta-description {
      font-size: 1.1rem;
      opacity: 0.9;
      margin-bottom: 30px;
    }

    .cta-buttons {
      display: flex;
      gap: 20px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .cta-button {
      padding: 16px 32px;
      border: none;
      border-radius: 12px;
      font-size: 1.1rem;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .cta-button.primary {
      background: white;
      color: #667eea;
    }

    .cta-button.primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
    }

    .cta-button.secondary {
      background: transparent;
      color: white;
      border: 2px solid white;
    }

    .cta-button.secondary:hover {
      background: white;
      color: #667eea;
    }

    .benefits-list {
      text-align: left;
      max-width: 400px;
      margin: 30px auto;
    }

    .benefits-list li {
      padding: 12px 0;
      display: flex;
      align-items: center;
      font-size: 1.1rem;
      color: #2c3e50;
    }

    .benefits-list li::before {
      content: '✓';
      color: #27ae60;
      font-weight: bold;
      margin-right: 15px;
      font-size: 1.3rem;
    }

    @media (max-width: 768px) {
      .subscription-header {
        padding: 40px 30px;
      }

      .subscription-header h1 {
        font-size: 2rem;
      }

      .subscription-body {
        padding: 40px 30px;
      }

      .feature-preview {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .cta-buttons {
        flex-direction: column;
        align-items: center;
      }

      .cta-button {
        width: 100%;
        max-width: 300px;
      }
    }
  </style>
</head>

<body>
  <div class="subscription-required-container">
    <div class="subscription-card">
      <div class="subscription-header">
        <div class="lock-icon">
          <i class="fas fa-lock"></i>
        </div>
        <h1>Premium Feature</h1>
        <p>This feature requires an active subscription to access</p>
      </div>

      <div class="subscription-body">
        <div class="feature-preview">
          <div class="feature-item">
            <div class="feature-icon">
              <i class="fas fa-id-card"></i>
            </div>
            <div class="feature-title">Business Cards</div>
            <div class="feature-description">Create professional digital business cards with custom branding and QR codes</div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="feature-title">Club Profiles</div>
            <div class="feature-description">Build your professional network with detailed club member profiles</div>
          </div>
        </div>

        <ul class="benefits-list">
          <li>Unlimited customization options</li>
          <li>Professional templates</li>
          <li>QR code generation</li>
          <li>Analytics and insights</li>
          <li>Priority support</li>
          <li>Export and sharing tools</li>
        </ul>

        <div class="cta-section">
          <div class="cta-title">Ready to Get Started?</div>
          <div class="cta-description">Choose from our flexible plans starting at just $9.99/month</div>
          
          <div class="cta-buttons">
            <a href="<?php echo e(route('subscriptions.plans')); ?>" class="cta-button primary">
              <i class="fas fa-crown"></i> View Plans
            </a>
            <a href="<?php echo e(route('dashboard')); ?>" class="cta-button secondary">
              <i class="fas fa-arrow-left"></i> Go Back
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Add some interactive animations
    document.addEventListener('DOMContentLoaded', function() {
      const featureItems = document.querySelectorAll('.feature-item');
      
      featureItems.forEach((item, index) => {
        setTimeout(() => {
          item.style.opacity = '0';
          item.style.transform = 'translateY(30px)';
          item.style.transition = 'all 0.6s ease';
          
          setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
          }, 100);
        }, index * 200);
      });
    });
  </script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/subscription-required.blade.php ENDPATH**/ ?>