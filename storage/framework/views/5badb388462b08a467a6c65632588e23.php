<!DOCTYPE html>
<html
  lang="en"
  data-wf-domain="evolution888.org"
  class="w-mod-js w-mod-ix"
>
<head>
  <meta charset="utf-8">
  <title>Profile – EVOLUTION888 Club</title>
  <meta name="description" content="Discover EVOLUTION888 Club — a digital ecosystem with cashback, partner programs, and exclusive privileges.">
  <meta content="Get in Touch – EVOLUTION888 Club" property="og:title">
  <meta property="og:description" content="Cashback, digital deals, and exclusive offers — only at EVOLUTION888 Store Club.">
  <meta content="https://cdn.prod.website-files.com/663b748d327c826a2952af46/66f7158e303df70c2274a356_techbeta-x-technology-app-webflow-template.png"
        property="og:image">
  <meta content="Contact – EVOLUTION888 Club – Intelligent Business Ecosystem" property="twitter:title">
  <meta property="twitter:description" content="Earn cashback and access exclusive digital products with EVOLUTION888 Store Club.">

  <meta content="https://cdn.prod.website-files.com/663b748d327c826a2952af46/66f7158e303df70c2274a356_techbeta-x-technology-app-webflow-template.png"
        property="twitter:image">
  <meta property="og:type" content="website">
  <meta content="summary_large_image" name="twitter:card">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <meta content="Webflow" name="generator">

  <!-- CSRF-токен для Laravel -->
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

  <link href="css/techbetatemplates.webflow.14600f26e.css"
        rel="stylesheet"
        type="text/css">
  <link href="https://cdn.prod.website-files.com/663b748d327c826a2952af46/663b95dbf2fa1adf0f4e484b_favicon-techbeta-webflow-template.svg"
        rel="shortcut icon"
        type="image/x-icon">
  <link href="https://cdn.prod.website-files.com/663b748d327c826a2952af46/663b95dd7fa9fba75b9cfcdc_webclip-techbeta-webflow-template.svg"
        rel="apple-touch-icon">

  <!-- Подключаем SweetAlert2 (CDN) -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

  <style>
    /* Отключаем мигающую обводку при клике с клавиатуры */
    .wf-force-outline-none[tabindex="-1"]:focus {
      outline: none;
    }
    .profile-picture-upload {
      grid-column: span 2;
      text-align: center;
      margin-bottom: 20px;
    }
    .profile-image-preview {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      overflow: hidden;
      margin: 0 auto 10px auto;
      background-color: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .profile-image-preview img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .w-webflow-badge {
      display: none !important;
    }
    .disabled-input {
      background-color: #f0f0f0;
      color: #888;
      cursor: not-allowed;
    }
    .primary-button.w-button {
      cursor: pointer;
    }
  </style>
</head>

<body>
<div class="page-wrapper">
  <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

  <section class="section top">
    <div class="w-layout-blockcontainer container-default w-container">
      <div class="inner-container _640px center-desktop---100-mbl">
        <div data-w-id="b9de3403-ef6f-e5b5-cf63-3e05414fd11e"
             style="transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1); opacity: 1; transform-style: preserve-3d;"
             class="inner-container _525px center">
          <div class="text-center">
            <h1 class="display-9 mid">
<span class="text-no-wrap">Club </span>
<span class="title-tag">Profile</span>
            </h1>
          </div>
        </div>

        <div class="mg-top-extra-large mg-top-48px---mbl">
          <div data-w-id="675455a7-b912-aa70-04fa-81af3bf76854"
               style="transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1); opacity: 1; transform-style: preserve-3d;"
               class="card form-wrapper">
            <div id="w-node-_675455a7-b912-aa70-04fa-81af3bf76855-a436e315" class="contact-form w-form">

              <div class="w-layout-grid grid-form">
                <!-- Блок загрузки аватара -->
                <div class="profile-picture-upload">
                  <label for="profile_image">Avatar</label>
                  <div class="profile-image-preview">
                    <img
                      id="profile-image-preview"
                      src="<?php echo e(auth()->user()->avatar ?? '/images/663e2ca10a1bbd076bf1a1e2_trusted-user-03-techbeta-x-webflow-template.png'); ?>"
                      alt="Profile Picture"
                    />
                  </div>
                  <input
                    type="file"
                    id="profile_image"
                    accept="image/*"
                    class="input w-input"
                  />
                </div>

                <!-- Полное имя (только кириллица) --
                <div>
                  <label for="full_name">Full Name</label>
                  <input
                    class="input w-input"
                    maxlength="256"
                    placeholder="John Michael Smith"
                    type="text"
                    id="full_name"
                    value="<?php echo e(auth()->user()->full_name ?? ''); ?>"
                    required
                    pattern="[А-Яа-яЁё\s-]+"
                    title="Используйте только кириллицу"
                  />
                </div>
-->				
				<div>
  <label for="full_name">Full Name (in English)</label>
  <input
    class="input w-input"
    maxlength="256"
    placeholder="John Michael Smith"
    type="text"
    id="full_name"
    name="full_name"
    value="<?php echo e(auth()->user()->full_name ?? ''); ?>"
    required
    pattern="[A-Za-z\s\-]+"
    title="Please use English letters only (A–Z)"
  />
  <small>Please enter your full name using English letters only.</small>
</div>

                <!-- Email -->
                <div>
                  <label for="email">Email</label>
                  <input
                    class="input w-input disabled-input"
                    maxlength="256"
                    placeholder="<EMAIL>"
                    type="email"
                    id="email"
                    value="<?php echo e(auth()->user()->email ?? ''); ?>"
                    required
                    readonly
                  />
                </div>

                <!-- Телефон (формат +7 (XXX) XXX-XX-XX) --
                <div>
                  <label for="phone">Phone Number</label>
                  <input
                    class="input w-input"
                    maxlength="256"
                    placeholder="****** 123 4567"
                    type="tel"
                    id="phone"
                    value="<?php echo e(auth()->user()->phone ?? ''); ?>"
                    required
                    pattern="^\+7\s?\(\d{3}\)\s?\d{3}-\d{2}-\d{2}$"
                    title="Введите номер в формате +1 XXX XXX-XX-XX"
                  />
                </div>
				-->
				
				<div>
  <label for="phone">Phone Number</label>
  <input
    class="input w-input"
    maxlength="20"
    placeholder="****** 123 4567"
    type="tel"
    id="phone"
    name="phone"
    value="<?php echo e(auth()->user()->phone ?? ''); ?>"
    required
    pattern="^\+\d{1,4}[\s\d\-]{6,}$"
    title="Enter your phone number in international format, e.g. ****** 123 4567"
  />
</div>


                <!-- Телеграм --
                <div>
                  <label for="telegram">Telegram</label>
                  <input
                    class="input w-input"
                    maxlength="256"
                    placeholder="@username"
                    type="text"
                    id="telegram"
                    value="<?php echo e(auth()->user()->telegram ?? ''); ?>"
                    required
                  />
                </div>
				-->
				
				<div>
  <label for="telegram">Telegram</label>
  <input
    class="input w-input"
    maxlength="32"
    placeholder="@username"
    type="text"
    id="telegram"
    name="telegram"
    value="<?php echo e(auth()->user()->telegram ?? ''); ?>"
    required
    pattern="^@?[a-zA-Z0-9_]{5,32}$"
    title="Enter your Telegram username (5–32 characters: letters, numbers, underscores)"
  />
</div>


                <!-- Дата рождения --
                <div>
                  <label for="birthdate">Дата рождения</label>
                  <input
                    class="input w-input"
                    maxlength="256"
                    placeholder="ГГГГ-ММ-ДД"
                    type="date"
                    id="birthdate"
                    value="<?php echo e(auth()->user()->birthdate ?? ''); ?>"
                    required
                  />
                </div>
-->

<!-- Date of Birth -->
<div>
  <label for="birthdate">Date of Birth</label>
  <input
    class="input w-input"
    maxlength="256"
    placeholder="YYYY-MM-DD"
    type="date"
    id="birthdate"
    name="birthdate"
    value="<?php echo e(auth()->user()->birthdate ?? ''); ?>"
    required
  />
</div>

                <!-- Пол (только М и Ж) --
                <div>
                  <label for="gender">Пол</label>
                  <select
                    class="input w-input"
                    id="gender"
                    required
                  >
                    <option value="" <?php if(!auth()->user()->gender): ?> selected <?php endif; ?>>Выберите пол</option>
                    <option value="male" <?php if(auth()->user()->gender === 'male'): ?> selected <?php endif; ?>>М</option>
                    <option value="female" <?php if(auth()->user()->gender === 'female'): ?> selected <?php endif; ?>>Ж</option>
                  </select>
                </div>
              </div>
			  -->
			  
<!-- Gender (only Male and Female) -->
<div>
  <label for="gender">Gender</label>
  <select
    class="input w-input"
    id="gender"
    name="gender"
    required
  >
    <option value="" <?php if(!auth()->user()->gender): ?> selected <?php endif; ?>>Select your gender</option>
    <option value="male" <?php if(auth()->user()->gender === 'male'): ?> selected <?php endif; ?>>Male</option>
    <option value="female" <?php if(auth()->user()->gender === 'female'): ?> selected <?php endif; ?>>Female</option>
  </select>
</div>			  

              <br>
              <div>
                <!-- Кнопка, при нажатии на которую вызываем JS-функцию updateProfile() -->
                <button
                  style="margin-left: auto; margin-right: auto;"
                  class="primary-button w-button"
                  onclick="updateProfile()"
                >
                  Save Settings
                </button>
              </div>

              <div class="success-message-wrapp w-form-done" tabindex="-1" role="region" aria-label="Contact Form success">
                <div>
                  <div class="icon-font-rounded success-message-icon"></div>
                  <div class="mg-top-default mg-top-24px---mbl">
                    <div class="text-titles">
                      <div class="display-5 mid">
                        Thank you! Our team will get back to <span class="text-no-wrap">you</span> shortly.
                      </div>
                    </div>
                  </div>
                  <div class="mg-top-extra-small">
                    <p>
We’ve received your message and will get back to you as soon as possible.  
Our team is committed to providing the best support, and we truly appreciate  
<span class="text-no-wrap">your time.</span>
                    </p>
                  </div>
                </div>
              </div>

              <div class="error-message-wrapper w-form-fail" tabindex="-1" role="region" aria-label="Contact Form failure">
                <div>Oops! Something went wrong while submitting the form.</div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>

<!-- Скрипты -->
<script src="js/jquery-3.5.1.min.dc5e7f18c8.js"
        type="text/javascript"
        integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="
        crossorigin="anonymous"></script>
<script src="js/webflow.3de03aa26.js" type="text/javascript"></script>

<script>
  // При выборе нового файла — сразу показываем превью
  const fileInput = document.getElementById('profile_image');
  const previewImg = document.getElementById('profile-image-preview');

  fileInput.addEventListener('change', async function() {
    if (this.files && this.files[0]) {
      const base64 = await readFileAsBase64(this.files[0]);
      previewImg.src = base64;
    }
  });

  // Функция для сохранения профиля
  async function updateProfile() {
    try {
      const fullName   = document.getElementById('full_name').value;
      const phone      = document.getElementById('phone').value;
      const telegram   = document.getElementById('telegram').value;
      const birthdate  = document.getElementById('birthdate').value;
      const gender     = document.getElementById('gender').value;

      let avatarBase64 = '';
      if (fileInput.files && fileInput.files[0]) {
        avatarBase64 = await readFileAsBase64(fileInput.files[0]);
      }

      const payload = {
        Full_Name: fullName,
        Phone: phone,
        Telegram: telegram,
        Birthdate: birthdate,
        Gender: gender,
      };

      if (avatarBase64) {
        payload.Avatar = avatarBase64;
      }

      const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

      const response = await fetch('/api/update_profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify(payload)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Error updating profile');
      }

      Swal.fire({
        icon: 'success',
        title: 'Profile updated successfully!',
        confirmButtonText: 'OK'
      });

    } catch (error) {
      Swal.fire({
        icon: 'error',
        title: 'Update Failed',
        text: error.message || 'Something went wrong',
        confirmButtonText: 'OK'
      });
    }
  }

  // Вспомогательная функция для чтения файла как base64
  function readFileAsBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = (err) => reject(err);
      reader.readAsDataURL(file);
    });
  }
</script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/dashboard/profile.blade.php ENDPATH**/ ?>