<?php $__env->startSection('title'); ?>
News Edit - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.18/summernote-lite.min.css" rel="stylesheet">

<style>
    .form-check-label {
        text-transform: capitalize;
    }
    .image-preview {
        width: 100px;
        height: 100px;
        border: 1px solid #ddd;
        margin-top: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }
    .image-preview img {
        width: 100%;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>

<!-- page title area start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Edit level</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><a href="<?php echo e(route('admin.categories.index')); ?>">All level's</a></li>
                    <li><span>Edit Level - <?php echo e($level->level); ?></span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- page title area end -->

<div class="main-content-inner">
    <div class="row">
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Edit Level - <?php echo e($level->category_name); ?></h4>
                    <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <form action="<?php echo e(route('admin.levels.update', $level->id)); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo method_field('PUT'); ?>
                        <?php echo csrf_field(); ?>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="title">Level Number</label>
                                <input type="number" class="form-control" id="level" name="level" placeholder="Enter level" value="<?php echo e($level->level); ?>" readonly>
                            </div>
                        </div>
						<div class="form-row">
							<div class="form-group col-md-6">
								<label for="name">Percentage</label>
								<input type="number" class="form-control" id="percentage" name="percentage" value="<?php echo e($level->percentage); ?>" placeholder="Enter percentage" required>
							</div>
						</div>
                        <button type="submit" class="btn btn-primary mt-4 pr-4 pl-4">Update News</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.18/summernote-lite.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $('#description').summernote({
            height: 300,   // высота редактора
            minHeight: null,   // минимальная высота редактора
            maxHeight: null,   // максимальная высота редактора
            focus: true    // установка фокуса на элемент
        });

        $('.select2').select2();
    });

    function previewImage() {
        const file = document.querySelector('#img').files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const imgElement = document.createElement('img');
                imgElement.src = e.target.result;
                document.querySelector('#imagePreview').innerHTML = '';
                document.querySelector('#imagePreview').appendChild(imgElement);
            };
            reader.readAsDataURL(file);
        } else {
            document.querySelector('#imagePreview').innerHTML = '<span>No image</span>';
        }
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/levels/edit.blade.php ENDPATH**/ ?>