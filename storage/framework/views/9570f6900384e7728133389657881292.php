<!DOCTYPE html>
<html lang="en" class="w-mod-js w-mod-ix">
<head>
  <meta charset="utf-8">
  <title>Социальные программы IMI клуба</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <script src="<?php echo e(asset('js/gtranslate-helper.js')); ?>"></script>
  <style>
    /* Определение CSS-переменных для обеспечения видимости элементов */
    :root {
      --accent-color: #007bff;    /* Ярко-синий фон для кнопок */
      --border-color: #ccc;       /* Светло-серый цвет границы */
      --secondary-text: #666;     /* Темно-серый для второстепенного текста */
    }
    
    .social-list {
      list-style: none;
      padding: 0;
      margin: 20px auto 0; /* Отступ сверху, чтобы список был ниже заголовка и поиска */
      max-width: 800px;
    }
    .social-item {
      background-color: #f9f9f9;
      border: 1px solid var(--border-color);
      border-radius: 10px;
      padding: 15px;
      margin-bottom: 15px;
      transition: box-shadow 0.3s ease;
    }
    .social-item:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    /* Ссылка-обертка для social проекта */
    .social-item a {
      display: flex;
      text-decoration: none;
      color: inherit;
      width: 100%;
    }
    .social-image {
      width: 120px;
      height: 80px;
      object-fit: cover;
      border-radius: 8px;
      margin-right: 20px;
      flex-shrink: 0;
    }
    .social-content {
      flex: 1;
    }
    .social-title {
      font-size: 20px;
      margin: 0 0 10px;
    }
    .social-description {
      font-size: 16px;
      line-height: 1.4;
      color: var(--secondary-text);
      margin: 0;
    }
    .social-date {
      font-size: 14px;
      color: var(--secondary-text);
      margin-top: 10px;
    }
    /* Стили для пагинации */
    .pagination {
      text-align: center;
      margin: 30px 0;
    }
    .page-btn {
      padding: var(--components--buttons--paddings--pd-regular) var(--components--buttons--paddings--pd-medium);
      grid-column-gap: var(--components--buttons--gaps--gap-small);
      grid-row-gap: var(--components--buttons--gaps--gap-small);
      border: var(--components--buttons--border-width--bw-default) solid var(--components--buttons-primary--border-color--b-light-mode);
      border-radius: var(--components--buttons--border-radius--br-regular);
      background-color: var(--components--buttons-primary--backgrounds--bg-light-mode);
      box-shadow: 0 2px 4px 0 var(--core--box-shadow--bs-primary-regular);
      color: var(--components--buttons-primary--text--text-light-mode);
      font-size: var(--core--font-size--displays--display-2);
      line-height: var(--core--line-height--regular);
      text-align: center;
      transform-style: preserve-3d;
      justify-content: center;
      align-items: center;
      font-weight: 500;
      text-decoration: none;
      transition: transform .3s;
      cursor: pointer;
    }
    .page-btn:hover {
      color: var(--components--buttons-primary--text--text-light-mode);
      transform: scale3d(.94, .94, 1.01);
    }
    .page-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    #page-info {
      font-size: 16px;
      margin: 0 10px;
      vertical-align: middle;
    }
  </style>
</head>
<body>
  <div class="page-wrapper">
    <!-- Верхняя навигация / хедер -->
    <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
  </div>

  <section class="section top small">
    <div class="w-container">
      <div data-w-id="0cdb6d37-381d-7c43-57fb-24882cc1868b" style="transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1); opacity: 1; transform-style: preserve-3d;" class="title-left---content-right center---mbl">
        <div class="width-100-mbl">
          <h2 class="display-8 mid">Социальные программы</h2>
        </div>
        <div class="position-relative mg-bottom-0 w-form">
          <label for="search" class="hidden">Поиск</label>
          <input class="input button-inside icon-inside w-input" maxlength="256" name="query" placeholder="Search projects…" type="search" id="search" required>
          <input type="submit" class="primary-button inside-input button-icon w-button" value="">
        </div>
      </div>
      <!-- Список social проектов (в виде UL) -->
      <ul class="social-list" id="social-list">
        <!-- Social проекты будут подгружаться динамически через JavaScript -->
      </ul>
      <!-- Пагинация social проектов -->
      <div class="pagination" id="social-pagination">
        <button id="prev-page-btn" class="page-btn" style="display: none;">← Назад</button>
        <span id="page-info"></span>
        <button id="next-page-btn" class="page-btn" style="display: none;">Далее →</button>
      </div>
    </div>
  </section>

  <footer>
    <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
  </footer>

  <!-- Скрипты -->
  <script
    src="js/jquery-3.5.1.min.dc5e7f18c8.js"
    type="text/javascript"
    integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="
    crossorigin="anonymous">
  </script>
  <script src="js/webflow.3de03aa26.js" type="text/javascript"></script>
  <script type="text/javascript">
    // Функция для форматирования даты в удобочитаемый формат
    function formatDateTime(datetimeStr) {
      const dateObj = new Date(datetimeStr);
      if (isNaN(dateObj.getTime())) return datetimeStr;
      return dateObj.toLocaleString([], { 
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
    
    $(document).ready(function () {
      let currentPage = 1;
      let totalPages = 1;
      const itemsPerPage = 10;

      function fetchSocial(page, search = '') {
        $.ajax({
          url: "/social_projects_search?page=" + page + "&search=" + encodeURIComponent(search),
          method: "GET",
          success: function (response) {
            // Очистка списка
            $("#social-list").empty();

            const socialItems = response.data || [];
            totalPages = response.meta ? response.meta.total_pages : 1;
            currentPage = response.meta ? response.meta.current_page : 1;

            // Рендер social проектов в виде списка
            socialItems.forEach(function (item) {
              // Ограничиваем описание 50 символами
              const truncatedDescription = item.description.length > 50
                ? item.description.substring(0, 50) + '...'
                : item.description;
              const socialHtml = `
                <li class="social-item">
                  <a href="/social_project_view?id=${item._id}">
                    <img src="${item.img}" alt="${item.name}" class="social-image">
                    <div class="social-content">
                      <h3 class="social-title">${item.name}</h3>
                      <p class="social-description">${truncatedDescription}</p>
                    </div>
                  </a>
                </li>
              `;
              $("#social-list").append(socialHtml);
            });

            updatePagination();
            
            // Запускаем перевод для нового контента
            if (window.GTranslateHelper) {
              setTimeout(() => {
                window.GTranslateHelper.handleDynamicContent(document.getElementById('social-list'));
              }, 500);
            }
          },
          error: function (err) {
            console.error("Ошибка при получении social проектов:", err);
          }
        });
      }

      function updatePagination() {
        $("#page-info").text("Page " + currentPage + " of " + totalPages);
        if (currentPage > 1) {
          $("#prev-page-btn").show();
        } else {
          $("#prev-page-btn").hide();
        }
        if (currentPage < totalPages) {
          $("#next-page-btn").show();
        } else {
          $("#next-page-btn").hide();
        }
      }

      $("#prev-page-btn").click(function () {
        if (currentPage > 1) {
          fetchSocial(--currentPage, $('#search').val().trim());
        }
      });
      $("#next-page-btn").click(function () {
        if (currentPage < totalPages) {
          fetchSocial(++currentPage, $('#search').val().trim());
        }
      });

      $('#search').on('input', function() {
        fetchSocial(1, $(this).val().trim());
      });

      // Первоначальная загрузка social проектов
      fetchSocial(currentPage);
    });
  </script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/social_projects_list.blade.php ENDPATH**/ ?>