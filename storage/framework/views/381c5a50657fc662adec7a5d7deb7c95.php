<?php $__env->startSection('title', 'Codes Page - Admin Panel'); ?>

<?php $__env->startSection('styles'); ?>
    <!-- Datatable CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.19/css/jquery.dataTables.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.18/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap.min.css">
<?php $__env->stopSection(); ?>
<style>
/* Скрываем текст, делая его прозрачным и задавая размытый тень-обводку */
.spoiler {
  color: transparent;
  text-shadow: 0 0 8px #ccc; /* опционально, "заблюрен" текст */
  transition: color 0.3s ease, text-shadow 0.3s ease;
  cursor: pointer; /* курсор, чтобы понятно было, что можно навести */
}

/* При наведении убираем прозрачность и "размытую" тень */
.spoiler:hover {
  color: #000;      /* цвет «по-настоящему» */
  text-shadow: none; /* убираем тень */
}
</style>
<?php $__env->startSection('admin-content'); ?>
    <!-- Page Title Area -->
    <div class="page-title-area">
        <div class="row align-items-center">
            <div class="col-sm-6">
                <div class="breadcrumbs-area clearfix">
                    <h4 class="page-title pull-left">Codes</h4>
                    <ul class="breadcrumbs pull-left">
                        <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                        <li><span>All categories</span></li>
                    </ul>
                </div>
            </div>
            <div class="col-sm-6 clearfix">
                <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
    <!-- Page Title Area End -->

    <!-- Main Content Area -->
    <div class="main-content-inner">
        <div class="row">
            <!-- Data Table Start -->
            <div class="col-12 mt-5">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title float-left">Code List</h4>
                        <?php if(auth()->user()->can('admin.codes.create')): ?>
                            <a class="btn btn-primary float-right mb-2" href="<?php echo e(route('admin.codes.create')); ?>">Create Code</a>
                        <?php endif; ?>
                        <div class="clearfix"></div>
                        
                        <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                        <div class="data-tables">
							<table id="dataTable" class="text-center">
								<thead class="bg-light text-capitalize">
									<tr>
										<th width="10%">Amount</th> 
										<th width="40%">Code</th>
										<th width="20%">Action</th>
									</tr>
								</thead>
								<tbody>
									<?php $__currentLoopData = $codes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
									<tr>
										<td><?php echo e($item->amount); ?> IMI</td>
										<td>
											<!-- Спойлер-код -->
											<span class="spoiler"><?php echo e($item->code); ?></span>
										</td>
										<td>
											<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('admin.codes.delete')): ?>
												<a class="btn-sm btn-danger text-white" href="<?php echo e(route('admin.codes.destroy', $item->id)); ?>" 
												   onclick="event.preventDefault(); document.getElementById('delete-form-<?php echo e($item->id); ?>').submit();">
												   Delete
												</a>
												<form id="delete-form-<?php echo e($item->id); ?>" action="<?php echo e(route('admin.codes.destroy', $item->id)); ?>" method="POST" style="display: none;">
													<?php echo method_field('DELETE'); ?>
													<?php echo csrf_field(); ?>
												</form>
											<?php endif; ?>
										</td>										
									</tr>
									<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
								</tbody>
							</table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Data Table End -->
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <!-- Datatable JS -->
    <script src="https://cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.18/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap.min.js"></script>
     
    <script>
        /* Datatable Initialization */
        $(document).ready(function() {
            $('#dataTable').DataTable({
                responsive: true
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/codes/index.blade.php ENDPATH**/ ?>