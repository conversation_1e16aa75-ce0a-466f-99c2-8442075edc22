<?php $__env->startSection('title'); ?>
Create Storage Product - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>

<div class="main-content-inner">
    <div class="row">
        <!-- form start -->
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Create New Product Storage</h4>
                    <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    
                    <form action="<?php echo e(route('admin.products_storage.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
						
                        <div class="form-row">
                            <div class="form-group col-md-12">
                                <label for="description">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="4" placeholder="Enter Product Storage Description" required></textarea>
                            </div>
                        </div>

                        <!-- Course Selection -->
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="product_id">Select Product</label>
                                <select class="form-control" id="product_id" name="product_id" required>
                                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($product->_id); ?>"><?php echo e($product->title); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary mt-4 pr-4 pl-4">Create Product Storage</button>
                    </form>
                </div>
            </div>
        </div>
        <!-- form end -->
    </div>
</div>
 
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/products_storage/create.blade.php ENDPATH**/ ?>