<?php $__env->startSection('title'); ?>
Edit product - Admin Panel
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.18/summernote-lite.min.css" rel="stylesheet">

<style>
    .form-check-label {
        text-transform: capitalize;
    }
    .image-preview {
        width: 100px;
        height: 100px;
        margin-top: 10px;
        border: 1px solid #dddddd;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #cccccc;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>

<!-- page title area start -->
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Edit product</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                    <li><a href="<?php echo e(route('admin.products.index')); ?>">All products</a></li>
                    <li><span>Edit product - <?php echo e($product->title); ?></span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<!-- page title area end -->

<div class="main-content-inner">
    <div class="row">
        <!-- form start -->
        <div class="col-12 mt-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Edit product - <?php echo e($product->title); ?></h4>
                    <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    
                    <form action="<?php echo e(route('admin.products.update', $product->id)); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo method_field('PUT'); ?>
                        <?php echo csrf_field(); ?>
                        
                        <!-- Title Input -->
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="title">Title (Max 64 Characters)</label>
                                <input type="text" class="form-control" id="title" name="title" value="<?php echo e($product->title); ?>" placeholder="Enter product Title" required>
                            </div>
                        </div>

                        <!-- Short Description Input -->
                        <div class="form-row">
                            <div class="form-group col-md-12">
                                <label for="short_description">Short Description</label>
                                <textarea class="form-control" id="short_description" name="short_description" rows="2" placeholder="Enter a short description for the product"><?php echo e($product->short_description); ?></textarea>
                            </div>
                        </div>

                        <!-- Full Description Input -->
                        <div class="form-row">
                            <div class="form-group col-md-12">
                                <label for="description">Full Description</label>
                                <textarea class="form-control" id="description" name="description" rows="4" placeholder="Enter Full Description" required><?php echo e($product->description); ?></textarea>
                            </div>
                        </div>

                        <!-- Price Input -->
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="price">Price</label>
                                <input type="number" class="form-control" id="price" name="price" value="<?php echo e($product->price); ?>" placeholder="Enter Price" required>
                            </div>
                        </div>

                        <!-- Category Input -->
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="category">Category</label>
                                <input type="text" class="form-control" id="category" name="category" value="<?php echo e($product->category); ?>" placeholder="Enter Category">
                            </div>
                        </div>

                        <!-- Image Upload -->
                        <div class="form-row">
                            <div class="form-group col-md-12">
                                <label for="img">Upload product Image</label>
                                <input type="file" class="form-control" id="img" name="img" accept="image/*" onchange="previewImage();">
                                <div class="image-preview" id="imagePreview">
                                    <?php if($product->img): ?>
                                        <img src="<?php echo e($product->img); ?>" alt="product Image" style="width: 100px; height: 100px;">
                                    <?php else: ?>
                                        <span>Image Preview</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary mt-4 pr-4 pl-4">Update product</button>
                    </form>
                </div>
            </div>
        </div>
        <!-- form end -->
        
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.18/summernote-lite.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $('#description').summernote({
            height: 300,   // высота редактора
            minHeight: null,   // минимальная высота редактора
            maxHeight: null,   // максимальная высота редактора
            focus: true    // установка фокуса на элемент
        });

        $('.select2').select2();
    });

    function previewImage() {
        const file = document.querySelector('#img').files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const imgElement = document.createElement('img');
                imgElement.src = e.target.result;
                imgElement.style.width = '100px';
                imgElement.style.height = '100px';
                document.querySelector('#imagePreview').innerHTML = '';
                document.querySelector('#imagePreview').appendChild(imgElement);
            };
            reader.readAsDataURL(file);
        }
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/products/edit.blade.php ENDPATH**/ ?>