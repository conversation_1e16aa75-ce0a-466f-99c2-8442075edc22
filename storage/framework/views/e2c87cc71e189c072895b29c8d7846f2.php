<?php $__env->startSection('title', 'KYC Requests - Admin Panel'); ?>

<?php $__env->startSection('styles'); ?>
    <!-- Datatable CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.19/css/jquery.dataTables.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.18/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap.min.css">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin-content'); ?>
    <div class="page-title-area">
        <div class="row align-items-center">
            <div class="col-sm-6">
                <div class="breadcrumbs-area clearfix">
                    <h4 class="page-title pull-left">KYC Requests</h4>
                    <ul class="breadcrumbs pull-left">
                        <li><a href="<?php echo e(route('admin.index')); ?>">Dashboard</a></li>
                        <li><span>All KYC</span></li>
                    </ul>
                </div>
            </div>
            <div class="col-sm-6 clearfix">
                <?php echo $__env->make('admin.dashboard.layouts.partials.logout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
    <!-- Page Title Area End -->

    <div class="main-content-inner">
        <div class="row">
            <!-- Data Table Start -->
            <div class="col-12 mt-5">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title float-left">KYC List</h4>
                        <div class="clearfix"></div>
                        
                        <?php echo $__env->make('admin.dashboard.layouts.partials.messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                        <div class="data-tables">
                            <table id="dataTable" class="text-center">
                                <thead class="bg-light text-capitalize">
                                    <tr>
                                        <th>User</th>
                                        <th>Full Name</th>
                                        <th>Status</th>
                                        <th width="30%">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $kycs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $kyc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e(optional($kyc->user)->email); ?></td>
                                        <td><?php echo e($kyc->full_name); ?></td>
                                        <td>
                                            <?php if($kyc->approved): ?>
                                                <span class="badge badge-success">Approved</span>
                                            <?php else: ?>
                                                <span class="badge badge-warning">Pending</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <!-- show -->
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('admin.kyc.view')): ?>
                                                <a class="btn-sm btn-info text-white" href="<?php echo e(route('admin.kyc.show', $kyc->id)); ?>">
                                                    Show
                                                </a>
                                            <?php endif; ?>

                                            <!-- approve (через update) -->
                                            <?php if(!$kyc->approved): ?>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('admin.kyc.approve')): ?>
                                                <form action="<?php echo e(route('admin.kyc.update', $kyc->id)); ?>" method="POST" style="display: inline;">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('PUT'); ?>
                                                    <input type="hidden" name="action" value="approve" />
                                                    <button type="submit" class="btn-sm btn-success text-white">
                                                        Approve
                                                    </button>
                                                </form>
                                                <?php endif; ?>
                                            <?php endif; ?>

                                            <!-- decline (edit -> form for reason) -->
                                            <?php if(!$kyc->approved): ?>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('admin.kyc.decline')): ?>
                                                <a class="btn-sm btn-danger text-white" href="<?php echo e(route('admin.kyc.edit', $kyc->id)); ?>">
                                                    Decline
                                                </a>
                                                <?php endif; ?>
                                            <?php endif; ?>

                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div> <!-- .data-tables -->
                    </div>
                </div>
            </div>
            <!-- Data Table End -->
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <!-- Datatable JS -->
    <script src="https://cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.18/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                responsive: true
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/club/resources/views/admin/kyc/index.blade.php ENDPATH**/ ?>