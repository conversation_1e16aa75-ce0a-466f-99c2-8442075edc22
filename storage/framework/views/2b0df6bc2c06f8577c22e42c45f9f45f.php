<!DOCTYPE html>
<html data-wf-domain="/" data-wf-page="663d095d8e07618c5381377b" data-wf-site="663b748d327c826a2952af46" data-wf-status="1" lang="en" class=" w-mod-js w-mod-ix">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><?php echo e(isset($businessCard) ? 'Edit' : 'Create'); ?> Business Card - EVOLUTION888</title>

  <!-- CSRF Token (Laravel) -->
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
  <!-- Main CSS -->
  <link rel="stylesheet" href="<?php echo e(asset('css/style.css')); ?>" />
  <link href="css/techbetatemplates.webflow.14600f26e.css?v=0.3" rel="stylesheet" type="text/css">

  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

  <style>
    .form-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    .form-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    .form-body {
      padding: 30px;
    }
    .form-group {
      margin-bottom: 25px;
    }
    .form-label {
      display: block;
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }
    .form-control {
      width: 100%;
      padding: 12px 15px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 14px;
      transition: border-color 0.3s;
    }
    .form-control:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    .form-control.error {
      border-color: #dc3545;
    }
    .error-message {
      color: #dc3545;
      font-size: 12px;
      margin-top: 5px;
    }
    .contacts-section {
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      background: #f8f9fa;
    }
    .contact-item {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
      align-items: flex-start;
    }
    .contact-type {
      width: 120px;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 13px;
    }
    .contact-value {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 13px;
    }
    .remove-contact {
      background: #dc3545;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
    }
    .add-contact {
      background: #28a745;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 13px;
      margin-top: 10px;
    }
    .preview-section {
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      background: #f8f9fa;
      margin-top: 20px;
    }
    .preview-card {
      max-width: 300px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    .preview-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      text-align: center;
    }
    .preview-logo {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      border: 2px solid white;
      margin-bottom: 10px;
      object-fit: cover;
    }
    .preview-body {
      padding: 20px;
    }
    .btn-primary {
      background: #007bff;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background 0.3s;
    }
    .btn-primary:hover {
      background: #0056b3;
    }
    .btn-secondary {
      background: #6c757d;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
      transition: background 0.3s;
    }
    .btn-secondary:hover {
      background: #545b62;
      text-decoration: none;
      color: white;
    }
    .form-actions {
      display: flex;
      gap: 15px;
      justify-content: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #e9ecef;
    }
    .file-upload {
      position: relative;
      display: inline-block;
      cursor: pointer;
    }
    .file-upload input[type=file] {
      position: absolute;
      left: -9999px;
    }
    .file-upload-label {
      display: inline-block;
      padding: 8px 16px;
      background: #f8f9fa;
      border: 1px solid #ddd;
      border-radius: 6px;
      cursor: pointer;
      font-size: 13px;
      transition: background 0.3s;
    }
    .file-upload-label:hover {
      background: #e9ecef;
    }
  </style>
</head>

<body>
  <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

  <div class="page-wrapper">
    <section class="section template-pages-hero" style="margin-top: -120px;">
      <div class="w-layout-blockcontainer container-default w-container">
        <div class="form-container">
          <div class="form-header">
            <h1 class="display-9 mid" style="margin: 0;">
              <?php echo e(isset($businessCard) ? 'Edit Business Card' : 'Create Business Card'); ?>

            </h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">
              <?php echo e(isset($businessCard) ? 'Update your digital business card' : 'Create your digital business card'); ?>

            </p>
          </div>

          <div class="form-body">
            <form id="businessCardForm" method="POST" action="<?php echo e(isset($businessCard) ? route('business-cards.update', $businessCard->id) : route('business-cards.store')); ?>" enctype="multipart/form-data">
              <?php echo csrf_field(); ?>
              <?php if(isset($businessCard)): ?>
                <?php echo method_field('PUT'); ?>
              <?php endif; ?>

              <!-- Basic Information -->
              <div class="form-group">
                <label class="form-label">Card Title *</label>
                <input type="text" name="title" class="form-control" 
                       value="<?php echo e(old('title', $businessCard->title ?? '')); ?>" 
                       placeholder="e.g., John Doe - CEO" required>
                <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <div class="error-message"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
              </div>

              <div class="form-group">
                <label class="form-label">Profile Name *</label>
                <input type="text" name="profile_name" class="form-control" 
                       value="<?php echo e(old('profile_name', $businessCard->profile_name ?? '')); ?>" 
                       placeholder="e.g., john-doe (will be used in URL)" 
                       <?php echo e(isset($businessCard) ? 'readonly' : ''); ?>>
                <small style="color: #6c757d; font-size: 12px;">
                  Your card will be available at: <?php echo e(url('/tag/')); ?>/<span id="profile-preview">your-name</span>
                </small>
                <?php $__errorArgs = ['profile_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <div class="error-message"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
              </div>

              <div class="form-group">
                <label class="form-label">Description</label>
                <textarea name="description" class="form-control" rows="3" 
                          placeholder="Brief description about yourself or your business"><?php echo e(old('description', $businessCard->description ?? '')); ?></textarea>
                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <div class="error-message"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
              </div>

              <!-- Logo Upload -->
              <div class="form-group">
                <label class="form-label">Logo/Photo</label>
                <div class="file-upload">
                  <input type="file" name="logo" id="logo-upload" accept="image/*">
                  <label for="logo-upload" class="file-upload-label">
                    <i class="fas fa-upload"></i> Choose Image
                  </label>
                </div>
                <?php if(isset($businessCard) && $businessCard->logo): ?>
                  <div style="margin-top: 10px;">
                    <img src="<?php echo e($businessCard->logo); ?>" alt="Current logo" style="width: 60px; height: 60px; border-radius: 8px; object-fit: cover;">
                    <small style="display: block; color: #6c757d; margin-top: 5px;">Current logo</small>
                  </div>
                <?php endif; ?>
                <?php $__errorArgs = ['logo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <div class="error-message"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
              </div>

              <!-- Contacts Section -->
              <div class="form-group">
                <label class="form-label">Contact Information</label>
                <div class="contacts-section">
                  <div id="contacts-container">
                    <?php if(isset($businessCard) && $businessCard->contacts): ?>
                      <?php $__currentLoopData = $businessCard->contacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="contact-item">
                          <select name="contacts[<?php echo e($index); ?>][type]" class="contact-type">
                            <option value="phone" <?php echo e($contact['type'] == 'phone' ? 'selected' : ''); ?>>Phone</option>
                            <option value="email" <?php echo e($contact['type'] == 'email' ? 'selected' : ''); ?>>Email</option>
                            <option value="website" <?php echo e($contact['type'] == 'website' ? 'selected' : ''); ?>>Website</option>
                            <option value="telegram" <?php echo e($contact['type'] == 'telegram' ? 'selected' : ''); ?>>Telegram</option>
                            <option value="whatsapp" <?php echo e($contact['type'] == 'whatsapp' ? 'selected' : ''); ?>>WhatsApp</option>
                            <option value="linkedin" <?php echo e($contact['type'] == 'linkedin' ? 'selected' : ''); ?>>LinkedIn</option>
                            <option value="instagram" <?php echo e($contact['type'] == 'instagram' ? 'selected' : ''); ?>>Instagram</option>
                            <option value="facebook" <?php echo e($contact['type'] == 'facebook' ? 'selected' : ''); ?>>Facebook</option>
                          </select>
                          <input type="text" name="contacts[<?php echo e($index); ?>][value]" class="contact-value" 
                                 value="<?php echo e($contact['value']); ?>" placeholder="Contact value">
                          <button type="button" class="remove-contact" onclick="removeContact(this)">
                            <i class="fas fa-times"></i>
                          </button>
                        </div>
                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                      <div class="contact-item">
                        <select name="contacts[0][type]" class="contact-type">
                          <option value="phone">Phone</option>
                          <option value="email">Email</option>
                          <option value="website">Website</option>
                          <option value="telegram">Telegram</option>
                          <option value="whatsapp">WhatsApp</option>
                          <option value="linkedin">LinkedIn</option>
                          <option value="instagram">Instagram</option>
                          <option value="facebook">Facebook</option>
                        </select>
                        <input type="text" name="contacts[0][value]" class="contact-value" placeholder="Contact value">
                        <button type="button" class="remove-contact" onclick="removeContact(this)">
                          <i class="fas fa-times"></i>
                        </button>
                      </div>
                    <?php endif; ?>
                  </div>
                  <button type="button" class="add-contact" onclick="addContact()">
                    <i class="fas fa-plus"></i> Add Contact
                  </button>
                </div>
                <?php $__errorArgs = ['contacts'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <div class="error-message"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
              </div>

              <!-- Form Actions -->
              <div class="form-actions">
                <a href="<?php echo e(route('business-cards.index')); ?>" class="btn-secondary">
                  <i class="fas fa-arrow-left"></i> Cancel
                </a>
                <button type="submit" class="btn-primary">
                  <i class="fas fa-save"></i> <?php echo e(isset($businessCard) ? 'Update Card' : 'Create Card'); ?>

                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>
  </div>

  <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

  <script>
    let contactIndex = <?php echo e(isset($businessCard) && $businessCard->contacts ? count($businessCard->contacts) : 1); ?>;

    // Update profile preview
    document.querySelector('input[name="profile_name"]').addEventListener('input', function() {
      document.getElementById('profile-preview').textContent = this.value || 'your-name';
    });

    // Add contact function
    function addContact() {
      const container = document.getElementById('contacts-container');
      const contactItem = document.createElement('div');
      contactItem.className = 'contact-item';
      contactItem.innerHTML = `
        <select name="contacts[${contactIndex}][type]" class="contact-type">
          <option value="phone">Phone</option>
          <option value="email">Email</option>
          <option value="website">Website</option>
          <option value="telegram">Telegram</option>
          <option value="whatsapp">WhatsApp</option>
          <option value="linkedin">LinkedIn</option>
          <option value="instagram">Instagram</option>
          <option value="facebook">Facebook</option>
        </select>
        <input type="text" name="contacts[${contactIndex}][value]" class="contact-value" placeholder="Contact value">
        <button type="button" class="remove-contact" onclick="removeContact(this)">
          <i class="fas fa-times"></i>
        </button>
      `;
      container.appendChild(contactItem);
      contactIndex++;
    }

    // Remove contact function
    function removeContact(button) {
      const contactItems = document.querySelectorAll('.contact-item');
      if (contactItems.length > 1) {
        button.parentElement.remove();
      } else {
        Swal.fire({
          icon: 'warning',
          title: 'Cannot Remove',
          text: 'You must have at least one contact method.'
        });
      }
    }

    // Form submission
    document.getElementById('businessCardForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const formData = new FormData(this);
      const submitButton = this.querySelector('button[type="submit"]');
      const originalText = submitButton.innerHTML;
      
      submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
      submitButton.disabled = true;

      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
      });

      $.ajax({
        url: this.action,
        method: this.method,
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
          if (response.status === 'success') {
            Swal.fire({
              icon: 'success',
              title: 'Success!',
              text: response.message,
              timer: 2000,
              showConfirmButton: false
            }).then(() => {
              window.location.href = response.redirect || '<?php echo e(route("business-cards.index")); ?>';
            });
          }
        },
        error: function(xhr) {
          submitButton.innerHTML = originalText;
          submitButton.disabled = false;
          
          if (xhr.status === 422) {
            const errors = xhr.responseJSON.errors;
            let errorMessage = 'Please fix the following errors:\n';
            Object.keys(errors).forEach(key => {
              errorMessage += `• ${errors[key][0]}\n`;
            });
            
            Swal.fire({
              icon: 'error',
              title: 'Validation Error',
              text: errorMessage
            });
          } else {
            Swal.fire({
              icon: 'error',
              title: 'Error!',
              text: xhr.responseJSON?.message || 'An error occurred while saving the business card.'
            });
          }
        }
      });
    });

    // Logo preview
    document.getElementById('logo-upload').addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
          // You can add preview functionality here
          console.log('Logo selected:', file.name);
        };
        reader.readAsDataURL(file);
      }
    });
  </script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/business-cards/create.blade.php ENDPATH**/ ?>