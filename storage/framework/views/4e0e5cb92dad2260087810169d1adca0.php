<!DOCTYPE html>
<html lang="en">
<head>
   <meta charset="UTF-8">
   <title>Contact Us</title>
   <!-- Дополнительные meta/links по необходимости -->

   <!-- Стили для сетки и кнопки (можно перенести в отдельный .css файл) -->
   <style>
      body {
         margin: 0;
         padding: 0;
         font-family: Arial, sans-serif;
         background-color: #f7f7f7;
      }
      .page-wrapper {
         min-height: 100vh;
         display: flex;
         flex-direction: column;
      }
      .section.top {
         padding: 40px 0;
      }
      .container-default.w-container {
         max-width: 1200px;
         margin: 0 auto;
         padding: 0 15px;
      }
      .inner-container {
         margin: 0 auto;
      }
      ._640px {
         max-width: 640px;
      }
      ._525px {
         max-width: 525px;
      }
      ._500px {
         max-width: 500px;
      }
      .center-desktop---100-mbl {
         text-align: center;
      }
      .center {
         text-align: center;
      }
      .text-center {
         text-align: center;
      }
      .display-9 {
         font-size: 2rem;
         font-weight: 700;
      }
      .mid {
         font-weight: 500;
      }
      .title-tag {
         color: #555;
         font-weight: 300;
         margin-right: 8px;
      }
      .text-no-wrap {
         white-space: nowrap;
      }
      .mg-top-small {
         margin-top: 10px;
      }
      .mg-top-16px---mbl {
         margin-top: 16px;
      }
      .mg-top-extra-large {
         margin-top: 60px;
      }
      .mg-top-48px---mbl {
         margin-top: 48px;
      }
      .card.form-wrapper {
         background: #fff;
         padding: 30px;
         border-radius: 6px;
         box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      }
      .contact-form {
         width: 100%;
      }
      .form.contact-form {
         width: 100%;
      }

      /* Сетка формы (2 колонки), промежутки между элементами */
      .w-layout-grid.grid-form {
         display: grid;
         grid-template-columns: 1fr 1fr;
         gap: 20px;
      }
      /* Каждая ячейка (кроме textarea и кнопки) */
      .w-layout-grid.grid-form > div {
         display: flex;
         flex-direction: column;
      }
      /* Текстовая область на 2 колонки */
      .text-area-wrapper {
         grid-column: 1 / 3;
      }
      /* Кнопка тоже на 2 колонки */
      .submit-wrapper {
         grid-column: 1 / 3;
         text-align: right; /* или center, если хотите по центру */
      }

      label {
         margin-bottom: 8px;
         font-weight: bold;
      }
      .input.w-input,
      .text-area.w-input {
         padding: 10px;
         border: 1px solid #ccc;
         border-radius: 4px;
         font-size: 14px;
      }
      .text-area.w-input {
         min-height: 100px;
         resize: vertical;
      }

      .primary-button.w-button {
         background: #000;
         color: #fff;
         padding: 12px 20px;
         border-radius: 4px;
         cursor: pointer;
         border: none;
         outline: none;
         font-size: 16px;
         transition: background 0.3s;
      }
      .primary-button.w-button:hover {
         background: #333;
      }

      /* Сообщения об успехе/ошибке */
      .success-message-wrapp.w-form-done,
      .error-message-wrapper.w-form-fail {
         display: none; /* При AJAX-отправке скрыты, но можно задействовать при обычной отправке */
         margin-top: 20px;
      }
      .success-message-wrapp.w-form-done.active,
      .error-message-wrapper.w-form-fail.active {
         display: block;
      }
      .icon-font-rounded {
         font-size: 24px;
         color: green;
      }
      .mg-top-default {
         margin-top: 20px;
      }
      .mg-top-24px---mbl {
         margin-top: 24px;
      }
      .text-titles {
         margin-bottom: 10px;
      }
      .display-5 {
         font-size: 1.5rem;
         font-weight: 700;
      }
      .mg-top-extra-small {
         margin-top: 10px;
      }

      /* Footer */
      .footer-wrapper {
         margin-top: auto; /* чтобы футер прижимался к низу, если контента мало */
         background: #f1f1f1;
         padding: 20px 0;
      }
   </style>
</head>
<body>
   <div class="page-wrapper">
      <!-- Подключение Navbar (если есть файл partials/navbar.blade.php) -->
      <?php echo $__env->make('partials.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

      <section class="section top">
         <div class="w-layout-blockcontainer container-default w-container">
            <div class="inner-container _640px center-desktop---100-mbl">
               <div class="inner-container _525px center">
                  <div class="text-center">
                     <h1 class="display-9 mid">
                        <span class="title-tag">Contact</span>
                        <span class="text-no-wrap">us today</span>
                     </h1>
                     <div class="mg-top-small mg-top-16px---mbl">
                        <div class="inner-container _500px center">
                           <p>Lorem ipsum dolor sit amet consectetur posuere purus vitae enim augue magna ut in posuere quis eu vitae nulla 
                              <span class="text-no-wrap">elit id faucibus.</span>
                           </p>
                        </div>
                     </div>
                  </div>
               </div>

               <div class="mg-top-extra-large mg-top-48px---mbl">
                  <div class="card form-wrapper">
                     <div class="contact-form w-form">
                        <!-- Форма отправляется методом POST на /contact/send -->
                        <form 
                           id="contactForm" 
                           method="POST" 
                           action="/contact/send" 
                           class="form contact-form" 
                           aria-label="Contact Form"
                        >
                           <?php echo csrf_field(); ?>
                           <div class="w-layout-grid grid-form">
                              <div>
                                 <label for="name">Full name</label>
                                 <input 
                                    class="input w-input" 
                                    maxlength="256" 
                                    name="Name" 
                                    placeholder="John Carter" 
                                    type="text" 
                                    id="name" 
                                    required
                                 >
                              </div>
                              <div>
                                 <label for="email">Email address</label>
                                 <input 
                                    class="input w-input" 
                                    maxlength="256" 
                                    name="Email" 
                                    placeholder="<EMAIL>"
                                    type="email" 
                                    id="email" 
                                    required
                                 >
                              </div>
                              <div>
                                 <label for="phone">Phone number</label>
                                 <input 
                                    class="input w-input" 
                                    maxlength="256" 
                                    name="Phone" 
                                    placeholder="(123) 456 - 7890" 
                                    type="tel" 
                                    id="phone" 
                                    required
                                 >
                              </div>
                              <div>
                                 <label for="company">Company</label>
                                 <input 
                                    class="input w-input" 
                                    maxlength="256" 
                                    name="Company" 
                                    placeholder="ex. Google" 
                                    type="text" 
                                    id="company" 
                                    required
                                 >
                              </div>
                              <!-- Поле сообщения на всю ширину (2 колонки) -->
                              <div class="text-area-wrapper">
                                 <label for="message">Message</label>
                                 <textarea 
                                    id="message" 
                                    name="Message" 
                                    maxlength="5000" 
                                    placeholder="Write your message here..." 
                                    required 
                                    class="text-area w-input"
                                 ></textarea>
                              </div>
                              <!-- Блок под кнопку (2 колонки) -->
                              <div class="submit-wrapper">
                                 <input 
                                    type="submit" 
                                    class="primary-button w-button" 
                                    value="Send Message"
                                 >
                              </div>
                           </div>
                        </form>

                        <!-- Сообщения при стандартной (не AJAX) отправке формы -->
                        <div 
                           class="success-message-wrapp w-form-done" 
                           tabindex="-1" 
                           role="region" 
                           aria-label="Contact Form success"
                        >
                           <div>
                              <div class="icon-font-rounded success-message-icon"></div>
                              <div class="mg-top-default mg-top-24px---mbl">
                                 <div class="text-titles">
                                    <div class="display-5 mid">
                                       Thank you! We’ll get back to 
                                       <span class="text-no-wrap">you soon</span>
                                    </div>
                                 </div>
                              </div>
                              <div class="mg-top-extra-small">
                                 <p>We have received your message and will get back to you as soon as possible. Our team is dedicated to providing the best support and we appreciate <span class="text-no-wrap">your patience.</span></p>
                              </div>
                           </div>
                        </div>

                        <div 
                           class="error-message-wrapper w-form-fail" 
                           tabindex="-1" 
                           role="region" 
                           aria-label="Contact Form failure"
                        >
                           <div>Oops! Something went wrong while submitting the form.</div>
                        </div>
                     </div>
                  </div>
               </div>

               <!-- Остальные секции, если нужны -->
            </div>
         </div>
      </section>

      <footer class="footer-wrapper">
         <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
      </footer>
   </div>

   <!-- Подключаем jQuery -->
   <script 
      src="https://code.jquery.com/jquery-3.5.1.min.js" 
      integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" 
      crossorigin="anonymous">
   </script>
   <!-- Подключаем ваш Webflow JS (если требуется) -->
   <script src="js/webflow.3de03aa26.js"></script>
   <!-- Подключаем SweetAlert -->
   <script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>

   <!-- Скрипт для AJAX-отправки формы -->
   <script>
   $(document).ready(function(){
      $('#contactForm').submit(function(e){
         e.preventDefault(); // Предотвращаем стандартную отправку формы

         $.ajax({
            url: $(this).attr('action'), // /contact/send
            method: 'POST',
            data: $(this).serialize(),
            success: function(response) {
               if(response.success) {
                  swal("Успех", response.message, "success");
                  $('#contactForm')[0].reset();
               } else {
                  swal("Ошибка", "Произошла ошибка при отправке формы", "error");
               }
            },
            error: function() {
               swal("Ошибка", "Произошла ошибка при отправке формы", "error");
            }
         });
      });
   });
   </script>
</body>
</html>
<?php /**PATH /var/www/club/resources/views/contact.blade.php ENDPATH**/ ?>